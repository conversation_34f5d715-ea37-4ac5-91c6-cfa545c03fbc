import {BaseResponseType, request} from '@baidu/bce-react-toolkit';

/**
 * IP黑白名单规则信息接口
 */
export interface IpRestrictionType {
  id?: string;
  enabled: boolean;
  name: string;
  description?: string;
  type: 'whitelist' | 'blacklist';
  scope?: string;
  ipAddresses: string[];
  createTime?: string;
}

/**
 * 分页返回结构
 */
export interface PageResponseType<T> {
  success: boolean;
  status: number;
  page?: {
    orderBy: string;
    order: string;
    pageNo: number;
    pageSize: number;
    totalCount: number;
    result: T[];
  };
  result?: T[];
}

/**
 * 创建IP黑白名单规则
 * @param instanceId 实例ID
 * @param data 请求体数据
 * @param region 地域
 * @returns 接口响应数据
 */
export function createIpRestriction(
  instanceId: string,
  data: Omit<IpRestrictionType, 'id' | 'createTime'>,
  region?: string
): Promise<BaseResponseType<IpRestrictionType>> {
  console.log('创建IP黑白名单规则，实例ID:', instanceId, '数据:', data);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/ipRestriction`,
    data,
    method: 'POST',
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 获取IP黑白名单规则列表
 * @param instanceId 实例ID
 * @param params 查询参数
 * @param region 地域
 * @returns IP黑白名单规则列表响应数据
 */
export function getIpRestrictionList(
  instanceId: string,
  params?: {
    name?: string;
  },
  region?: string
): Promise<{
  success: boolean;
  status: number;
  result: IpRestrictionType[];
}> {
  console.log('获取IP黑白名单规则列表，实例ID:', instanceId, '查询参数:', params);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/ipRestrictionList`,
    params,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 获取IP黑白名单规则详情
 * @param instanceId 实例ID
 * @param ipRestrictionId IP黑白名单规则ID
 * @param region 地域
 * @returns 接口响应数据
 */
export function getIpRestrictionDetail(
  instanceId: string,
  ipRestrictionId: string,
  region?: string
): Promise<BaseResponseType<IpRestrictionType>> {
  console.log('获取IP黑白名单规则详情，实例ID:', instanceId, '规则ID:', ipRestrictionId);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/ipRestriction/${ipRestrictionId}`,
    method: 'GET',
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 更新IP黑白名单规则
 * @param instanceId 实例ID
 * @param ipRestrictionId IP黑白名单规则ID
 * @param data 请求体数据
 * @param region 地域
 * @returns 接口响应数据
 */
export function updateIpRestriction(
  instanceId: string,
  ipRestrictionId: string,
  data: Omit<IpRestrictionType, 'id' | 'createTime'>,
  region?: string
): Promise<BaseResponseType<IpRestrictionType>> {
  console.log('更新IP黑白名单规则，实例ID:', instanceId, '规则ID:', ipRestrictionId, '数据:', data);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/ipRestriction/${ipRestrictionId}`,
    data,
    method: 'PUT',
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 删除IP黑白名单规则
 * @param instanceId 实例ID
 * @param ipRestrictionId IP黑白名单规则ID
 * @param region 地域
 * @returns 接口响应数据
 */
export function deleteIpRestriction(
  instanceId: string,
  ipRestrictionId: string,
  region?: string
): Promise<BaseResponseType<any>> {
  console.log('删除IP黑白名单规则，实例ID:', instanceId, '规则ID:', ipRestrictionId);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/ipRestriction/${ipRestrictionId}`,
    method: 'DELETE',
    headers: {
      'X-Region': region || ''
    }
  });
}