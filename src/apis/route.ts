import {BaseResponseType, request} from '@baidu/bce-react-toolkit';

/**
 * 路由信息接口
 */
export interface RouteType {
  routeName: string;
  routeStatus: string;
  matchPath: {
    matchType: string;
    value: string;
  };
  targetService: Array<{
    serviceName: string;
    namespace: string;
    servicePort: number;
    requestRatio?: number;
    modelName?: string;
  }>;
  createTime: string;
  srcProduct?: string; // 来源产品，如aipom、aibox等
}

/**
 * 路由详情接口
 */
export interface RouteDetailType {
  routeName: string;
  createTime: string;
  updateTime: string;
  multiService?: boolean;
  trafficDistributionStrategy?: 'ratio' | 'model_name';
  srcProduct?: string; // 来源产品，如aipom、aibox等
  matchRules: {
    pathRule: {
      matchType: string;
      value: string;
      caseSensitive: boolean;
    };
    methods: string[];
    headers: Array<{
      key: string;
      matchType: string;
      value: string;
    }>;
    queryParams: Array<{
      key: string;
      matchType: string;
      value: string;
    }>;
  };
  rewrite?: {
    enabled: boolean;
    path?: string;
  };
  targetService: {
    serviceSource: string;
    serviceName: string;
    namespace: string;
    servicePort: number;
    loadBalanceAlgorithm: string;
    hashType?: string;
    hashKey?: string;
    requestRatio?: number;
    modelName?: string;
  } | Array<{
    serviceSource: string;
    serviceName: string;
    namespace: string;
    servicePort: number;
    loadBalanceAlgorithm: string;
    hashType?: string;
    hashKey?: string;
    requestRatio?: number;
    modelName?: string;
  }>;
  authEnabled: boolean;
  allowedConsumers: string[];
  tokenRateLimit?: {
    rule_name?: string;
    enabled: boolean;
    rule_items?: Array<{
      match_condition: {
        type: 'consumer' | 'header' | 'query_param';
        key: string;
        value: string;
      };
      limit_config: {
        time_unit: 'second' | 'minute' | 'hour' | 'day';
        token_amount: number;
      };
    }>;
  };
  timeoutPolicy?: {
    enabled: boolean;
    timeout?: number;
  };
  retryPolicy?: {
    enabled: boolean;
    retryConditions?: string;
    numRetries?: number;
  };
}

/**
 * 获取路由列表
 * @param instanceId 实例ID
 * @param params 请求参数
 * @param region 地域
 * @returns 路由列表响应数据
 */
export function getRouteList(
  instanceId: string,
  params?: {
    pageNo: number;
    pageSize: number;
    orderBy: string;
    order: string;
    routeName?: string;
  },
  region?: string
): Promise<{
  success: boolean;
  status: number;
  page: {
    pageNo: number;
    pageSize: number;
    orderBy: string;
    order: string;
    totalCount: number;
    result: RouteType[];
  };
}> {
  console.log('获取路由列表, instanceId:', instanceId, '请求参数:', params);
  return request({
    url: `/api/aigw/v1/aigateway/cluster/${instanceId}/route`,
    params,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 获取服务列表
 * @param instanceId 实例ID
 * @param serviceSource 服务来源
 * @param region 地域
 * @returns 服务列表响应数据
 */
export function getServicesBySource(
  instanceId: string,
  serviceSource: string,
  region?: string
): Promise<any> {
  console.log('获取服务列表, instanceId:', instanceId, 'serviceSource:', serviceSource);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/service`,
    params: {
      serviceSource
    },
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 创建路由
 * @param instanceId 实例ID
 * @param clusterId 集群ID
 * @param data 路由数据
 * @returns 路由创建响应数据
 */
export function createRoute(
  instanceId: string,
  clusterId: string,
  data: {
    routeName: string;
    matchRules: {
      pathRule: {
        matchType: 'prefix' | 'exact';
        value: string;
        caseSensitive?: boolean;
      };
      methods: string[];
      headers?: Array<{
        key: string;
        matchType: 'prefix' | 'exact';
        value: string;
      }>;
      queryParams?: Array<{
        key: string;
        matchType: 'prefix' | 'exact';
        value: string;
      }>;
    };
    multiService?: boolean;
    trafficDistributionStrategy?: 'ratio' | 'model_name';
    targetService: {
      serviceSource: string;
      serviceName: string;
      namespace: string;
      servicePort: number;
      loadBalanceAlgorithm?: 'round-robin' | 'least-conn' | 'random' | 'consistent-hash';
      hashType?: 'header' | 'query_param' | 'ip' | 'cookie';
      hashKey?: string;
    } | Array<{
      serviceSource: string;
      serviceName: string;
      namespace: string;
      servicePort: number;
      loadBalanceAlgorithm?: 'round-robin' | 'least-conn' | 'random' | 'consistent-hash';
      hashType?: 'header' | 'query_param' | 'ip' | 'cookie';
      hashKey?: string;
      requestRatio?: number;
      modelName?: string;
    }>;
    timeoutPolicy?: {
      enabled: boolean;
      timeout?: number;
    };
    retryPolicy?: {
      enabled: boolean;
      retryConditions?: string;
      numRetries?: number;
    };
    rewrite?: {
      enabled: boolean;
      path?: string;
    };
    authEnabled?: boolean;
    allowedConsumers?: string[];
    tokenRateLimit?: {
      enabled: boolean;
      rule_items?: Array<{
        match_condition: {
          type: 'consumer' | 'header' | 'query_param';
          key: string;
          value: string;
        };
        limit_config: {
          time_unit: 'second' | 'minute' | 'hour' | 'day';
          token_amount: number;
        };
      }>;
    };
  },
  region?: string
): Promise<{
  success: boolean;
  status: number;
  result?: {
    routeId: string;
    routeName: string;
    matchPath: string;
    serviceName: string;
    createTime: string;
    multiService: boolean;
    trafficDistributionStrategy?: 'ratio' | 'model_name';
    targetService: any;
    tokenRateLimit?: {
      rule_name?: string;
      enabled: boolean;
      rule_items?: Array<{
        match_condition: {
          type: 'consumer' | 'header' | 'query_param';
          key: string;
          value: string;
        };
        limit_config: {
          time_unit: 'second' | 'minute' | 'hour' | 'day';
          token_amount: number;
        };
      }>;
    };
    timeoutPolicy?: {
      enabled: boolean;
      timeout?: number;
    };
    retryPolicy?: {
      enabled: boolean;
      retryConditions?: string;
      numRetries?: number;
    };
  };
  message?: string | { global?: string };
  code?: string;
}> {
  console.log('创建路由, instanceId:', instanceId, 'clusterId:', clusterId, '请求数据:', data);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/${clusterId}/route`,
    method: 'POST',
    data,
    headers: {
      'X-Region': region || '',
      'Content-Type': 'application/json'
    }
  });
}

/**
 * 删除路由
 * @param instanceId 实例ID
 * @param routeName 路由名称
 * @param region 地域代码，用于设置X-Region请求头
 * @returns 删除路由响应数据
 */
export function deleteRoute(
  instanceId: string,
  routeName: string,
  region?: string
): Promise<{
  success: boolean;
  status: number;
  result?: {
    routeId: string;
    routeName: string;
    deletedTime: string;
  };
  message?: string;
}> {
  console.log('删除路由, instanceId:', instanceId, 'routeName:', routeName);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/${routeName}/route/detail`,
    method: 'DELETE',
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 获取路由详情
 * @param instanceId 实例ID
 * @param routeName 路由名称
 * @param region 地域
 * @returns 路由详情响应数据
 */
export function getRouteDetail(
  instanceId: string,
  routeName: string,
  region?: string
): Promise<{
  success: boolean;
  status: number;
  result: RouteDetailType;
}> {
  console.log('获取路由详情, instanceId:', instanceId, 'routeName:', routeName);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/${routeName}/route/detail`,
    headers: {
      'X-Region': region || ''
    }
  });
}

/**
 * 更新路由
 * @param instanceId 实例ID
 * @param routeName 路由名称
 * @param data 路由数据
 * @param region 地域
 * @returns 路由更新响应数据
 */
export function updateRoute(
  instanceId: string,
  routeName: string,
  data: {
    matchRules: {
      pathRule: {
        matchType: string;
        value: string;
        caseSensitive?: boolean;
      };
      methods?: string[];
      headers?: Array<{
        key: string;
        matchType: string;
        value: string;
      }>;
      queryParams?: Array<{
        key: string;
        matchType: string;
        value: string;
      }>;
    };
    rewrite?: {
      enabled: boolean;
      path?: string;
    };
    multiService?: boolean;
    trafficDistributionStrategy?: 'ratio' | 'model_name';
    targetService: {
      serviceSource: string;
      serviceName: string;
      namespace: string;
      servicePort: number;
      loadBalanceAlgorithm?: string;
    } | Array<{
      serviceSource: string;
      serviceName: string;
      namespace: string;
      servicePort: number;
      loadBalanceAlgorithm?: string;
      requestRatio?: number;
      modelName?: string;
    }>;
    authEnabled?: boolean;
    allowedConsumers?: string[];
    tokenRateLimit?: {
      enabled: boolean;
      rule_items?: Array<{
        match_condition: {
          type: 'consumer' | 'header' | 'query_param';
          key: string;
          value: string;
        };
        limit_config: {
          time_unit: 'second' | 'minute' | 'hour' | 'day';
          token_amount: number;
        };
      }>;
    };
  },
  region?: string
): Promise<{
  success: boolean;
  status: number;
  result?: {
    routeName: string;
    updateTime: string;
    multiService?: boolean;
    trafficDistributionStrategy?: 'ratio' | 'model_name';
    targetService?: any;
    tokenRateLimit?: {
      rule_name?: string;
      enabled: boolean;
      rule_items?: Array<{
        match_condition: {
          type: 'consumer' | 'header' | 'query_param';
          key: string;
          value: string;
        };
        limit_config: {
          time_unit: 'second' | 'minute' | 'hour' | 'day';
          token_amount: number;
        };
      }>;
    };
  };
  message?: string;
}> {
  console.log('更新路由, instanceId:', instanceId, 'routeName:', routeName, '请求数据:', data);
  return request({
    url: `/api/aigw/v1/aigateway/${instanceId}/${routeName}/route/detail`,
    method: 'PUT',
    data,
    headers: {
      'X-Region': region || '',
      'Content-Type': 'application/json'
    }
  });
} 