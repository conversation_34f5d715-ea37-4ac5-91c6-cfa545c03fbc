import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getQueryParams, useRegion } from '@baidu/bce-react-toolkit';
import { Button, Form, Input, Alert, Select, Checkbox, Table, Switch, Transfer, Modal, Space, toast, Loading, Empty, Tooltip, Tag, Radio, InputNumber } from 'acud';
import { OutlinedLeft, OutlinedDelete, OutlinedPlus, OutlinedQuestionCircle } from 'acud-icon';
import { useRequest } from 'ahooks';

import urls from '@/utils/urls';
import styles from './index.module.less';
import { getServicesBySource, getRouteDetail, updateRoute } from '@/apis/route';
import { getConsumerList } from '@/apis/consumer';
import { getServicePorts } from '@/apis/service';
import { getRegistrationInstance } from '@/apis/instance';

const { Option } = Select;

// 类型定义
interface ServiceType {
  key: string;
  serviceSource: string;
  serviceName: string;
  namespace: string;
  servicePort: number | null;
  loadBalanceAlgorithm: string;
  hashType?: string;
  hashKey?: string;
  requestRatio?: number;
  modelName?: string;
}

interface CurrentEditServiceType {
  key: string;
  serviceName: string;
  loadBalanceAlgorithm: string;
  hashType?: string;
  hashKey?: string;
  isMultiService: boolean;
}

interface ModalFormErrorsType {
  loadBalanceAlgorithm?: string;
  hashType?: string;
  hashKey?: string;
}

const RouteEdit: React.FC = () => {
  const { instanceId, routeName } = getQueryParams();
  const { region } = useRegion();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  // 实例详情状态
  const [instanceDetail, setInstanceDetail] = useState<any>({});

  // 待处理的服务来源（等待实例详情加载完成后获取服务列表）
  const [pendingServiceSource, setPendingServiceSource] = useState<string>('');

  // 基础配置状态
  const [routeNameValue, setRouteNameValue] = useState('');

  // 路由规则状态
  const [caseSensitive, setCaseSensitive] = useState(true);
  const [headerParams, setHeaderParams] = useState<Array<{ key: string; matchType: string; value: string; }>>([]);
  const [queryParams, setQueryParams] = useState<Array<{ key: string; matchType: string; value: string; }>>([]);

  // 路径重写状态
  const [rewriteEnabled, setRewriteEnabled] = useState(false);
  const [rewriteMode, setRewriteMode] = useState('standard'); // 目前只支持标准模式
  const [rewritePath, setRewritePath] = useState('');

  // 多服务相关状态
  const [multiService, setMultiService] = useState(false);
  const [trafficDistributionStrategy, setTrafficDistributionStrategy] = useState<'ratio' | 'model_name'>('ratio');
  const [targetServices, setTargetServices] = useState<ServiceType[]>([]);

  // 单服务状态（保持向后兼容）
  const [targetService, setTargetService] = useState<ServiceType>({
    key: '0',
    serviceSource: 'CCE',
    serviceName: '',
    namespace: '',
    servicePort: null,
    loadBalanceAlgorithm: 'round-robin',
    hashType: undefined,
    hashKey: undefined
  });

  // Token限流相关状态
  const [tokenRateLimitEnabled, setTokenRateLimitEnabled] = useState(false);
  const [rateLimitStrategies, setRateLimitStrategies] = useState<Array<{
    key: string;
    matchCondition: {
      type: 'consumer' | 'header' | 'query_param';
      key: string;
      value: string;
    };
    limitConfig: {
      timeUnit: 'second' | 'minute' | 'hour' | 'day';
      tokenAmount: number;
    };
  }>>([]);

  // 超时策略状态
  const [timeoutPolicyEnabled, setTimeoutPolicyEnabled] = useState(false);
  const [timeoutValue, setTimeoutValue] = useState<number>(60);

  // 重试策略状态
  const [retryPolicyEnabled, setRetryPolicyEnabled] = useState(false);
  const [retryConditions, setRetryConditions] = useState<string[]>(['connect-failure', 'refused-stream', 'retriable-status-codes', 'cancelled', 'unavailable']);
  const [retryAttempts, setRetryAttempts] = useState<number>(3);

  // 认证授权状态
  const [authEnabled, setAuthEnabled] = useState(false);
  const [availableConsumers, setAvailableConsumers] = useState<any[]>([]);
  const [selectedConsumerNames, setSelectedConsumerNames] = useState<string[]>([]);
  // 记录原始授权的消费者，用于显示未授权标签
  const [originalAuthorizedConsumers, setOriginalAuthorizedConsumers] = useState<string[]>([]);

  // 路由来源产品状态
  const [srcProduct, setSrcProduct] = useState<string>('');

  // 选项数据
  const [serviceOptions, setServiceOptions] = useState<any[]>([]);

  // 匹配类型选项
  const matchTypeOptions = [
    { label: '前缀匹配', value: 'prefix' },
    { label: '精确匹配', value: 'exact' },
  ];

  // HTTP方法选项
  const httpMethodOptions = [
    { label: 'GET', value: 'GET' },
    { label: 'POST', value: 'POST' },
    { label: 'PUT', value: 'PUT' },
    { label: 'DELETE', value: 'DELETE' },
    { label: 'OPTIONS', value: 'OPTIONS' },
    { label: 'HEAD', value: 'HEAD' },
    { label: 'PATCH', value: 'PATCH' }
  ];

  // 负载均衡算法选项
  const loadBalanceOptions = [
    { label: '轮询', value: 'round-robin' },
    { label: '随机', value: 'random' },
    { label: '最小连接数', value: 'least-conn' },
    { label: '哈希一致性', value: 'consistent-hash' },
  ];

  // 哈希类型选项
  const hashTypeOptions = [
    { label: '基于Header', value: 'header' },
    { label: '基于请求参数', value: 'query_param' },
    { label: '基于源IP', value: 'ip' },
    { label: '基于Cookie', value: 'cookie' },
  ];

  // 重试条件选项
  const retryConditionOptions = [
    { label: '5xx', value: '5xx', description: '后端服务返回任何 5xx 响应，或发生连接断开、重置、读取超时事件' },
    { label: 'reset', value: 'reset', description: '发生连接断开、重置、读取超时事件' },
    { label: 'connect-failure', value: 'connect-failure', description: '请求连接断开' },
    { label: 'refused-stream', value: 'refused-stream', description: '后端服务以 REFUSED_STREAM 错误代码来重置连接' },
    { label: 'retriable-status-codes', value: 'retriable-status-codes', description: '后端服务响应错误的 HTTP 状态的任何上游请求的重试状态码' },
    { label: 'cancelled', value: 'cancelled', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 cancelled' },
    { label: 'deadline-exceeded', value: 'deadline-exceeded', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 deadline-exceeded' },
    { label: 'internal', value: 'internal', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 internal' },
    { label: 'resource-exhausted', value: 'resource-exhausted', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 resource-exhausted' },
    { label: 'unavailable', value: 'unavailable', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 unavailable' },
  ];

  // 添加服务端口列表状态
  const [servicePorts, setServicePorts] = useState<string[]>([]);
  const [servicePortsLoading, setServicePortsLoading] = useState(false);

  // 添加负载均衡算法弹窗状态
  const [loadBalanceModalVisible, setLoadBalanceModalVisible] = useState(false);
  const [currentEditService, setCurrentEditService] = useState<CurrentEditServiceType | null>(null);
  const [modalFormErrors, setModalFormErrors] = useState<ModalFormErrorsType>({});

  // 获取实例详情
  const fetchInstanceDetail = async () => {
    try {
      // console.log('请求实例详情，instanceId:', instanceId, '地域:', region);
      const res = await getRegistrationInstance(instanceId, region);
      // console.log('获取实例详情成功:', res);
      if (res?.success && res?.result) {
        setInstanceDetail(res.result);
      }
    } catch (error) {
      console.error('获取实例详情失败:', error);
    }
  };

  // 获取路由详情
  const { loading: detailLoading, run: fetchRouteDetail } = useRequest(
    () => {
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      return getRouteDetail(instanceId as string, routeName as string, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.result) {
          const detail = res.result;

          // 设置基础信息
          setRouteNameValue(detail.routeName);

          // 设置路由来源产品
          setSrcProduct(detail.srcProduct || '');

          // 设置路由规则相关状态
          const { matchRules } = detail;
          if (matchRules) {
            // 设置路径匹配规则
            if (matchRules.pathRule) {
              setCaseSensitive(matchRules.pathRule.caseSensitive ?? true);
              form.setFieldsValue({
                pathMatchType: matchRules.pathRule.matchType,
                pathValue: matchRules.pathRule.value
              });
            }

            // 设置HTTP方法
            if (matchRules.methods && Array.isArray(matchRules.methods)) {
              form.setFieldsValue({ methods: matchRules.methods });
            }

            // 设置请求头参数
            if (matchRules.headers && Array.isArray(matchRules.headers)) {
              setHeaderParams(matchRules.headers);
            }

            // 设置请求参数
            if (matchRules.queryParams && Array.isArray(matchRules.queryParams)) {
              setQueryParams(matchRules.queryParams);
            }
          }

          // 设置路径重写配置
          if (detail.rewrite) {
            setRewriteEnabled(detail.rewrite.enabled || false);
            if (detail.rewrite.enabled && detail.rewrite.path) {
              setRewritePath(detail.rewrite.path);
            }
          }

          // 设置目标服务
          if (detail.targetService) {
            const { targetService: detailTargetService } = detail;

            // 设置多服务状态
            setMultiService(detail.multiService || false);
            if (detail.trafficDistributionStrategy) {
              setTrafficDistributionStrategy(detail.trafficDistributionStrategy);
            }

            // 处理单服务和多服务的情况
            if (Array.isArray(detailTargetService)) {
              // 多服务模式
              const services = detailTargetService.map((service, index) => ({
                key: index.toString(),
                serviceSource: service.serviceSource || 'CCE',
                serviceName: service.serviceName || '',
                namespace: service.namespace || '',
                servicePort: service.servicePort || null,
                loadBalanceAlgorithm: service.loadBalanceAlgorithm || 'round-robin',
                hashType: (service as any).hashType,
                hashKey: (service as any).hashKey || '',
                requestRatio: service.requestRatio,
                modelName: service.modelName
              }));
              setTargetServices(services);

              // 获取第一个服务的服务列表（延迟到实例详情加载完成后）
              if (services.length > 0 && services[0].serviceSource) {
                // 如果实例详情已加载，立即获取服务列表；否则等待实例详情加载完成
                if (instanceDetail?.region) {
                  fetchServices(services[0].serviceSource);
                } else {
                  // 保存服务来源，等待实例详情加载完成后再获取
                  setPendingServiceSource(services[0].serviceSource);
                }
              }
            } else {
              // 单服务模式
              setTargetService({
                key: '0',
                serviceSource: detailTargetService.serviceSource || 'CCE',
                serviceName: detailTargetService.serviceName || '',
                namespace: detailTargetService.namespace || '',
                servicePort: detailTargetService.servicePort || null,
                loadBalanceAlgorithm: detailTargetService.loadBalanceAlgorithm || 'round-robin',
                hashType: (detailTargetService as any).hashType,
                hashKey: (detailTargetService as any).hashKey
              });

              // 获取服务列表（延迟到实例详情加载完成后）
              if (detailTargetService.serviceSource) {
                // 如果实例详情已加载，立即获取服务列表；否则等待实例详情加载完成
                if (instanceDetail?.region) {
                  fetchServices(detailTargetService.serviceSource);
                } else {
                  // 保存服务来源，等待实例详情加载完成后再获取
                  setPendingServiceSource(detailTargetService.serviceSource);
                }
              }
            }
          }

          // 设置认证授权
          setAuthEnabled(detail.authEnabled || false);
          if (detail.allowedConsumers && Array.isArray(detail.allowedConsumers)) {
            // 存储后端返回的消费者名称
            setSelectedConsumerNames(detail.allowedConsumers);
            // 同时保存原始授权消费者列表
            setOriginalAuthorizedConsumers(detail.allowedConsumers);
          }

          // 设置Token限流
          if (detail.tokenRateLimit) {
            setTokenRateLimitEnabled(detail.tokenRateLimit.enabled || false);
            if (detail.tokenRateLimit.rule_items && Array.isArray(detail.tokenRateLimit.rule_items)) {
              const strategies = detail.tokenRateLimit.rule_items.map((item, index) => ({
                key: index.toString(),
                matchCondition: {
                  type: item.match_condition.type,
                  key: item.match_condition.key,
                  value: item.match_condition.value
                },
                limitConfig: {
                  timeUnit: item.limit_config.time_unit,
                  tokenAmount: item.limit_config.token_amount
                }
              }));
              setRateLimitStrategies(strategies);
            }
          }

          // 设置超时策略
          if ((detail as any).timeoutPolicy) {
            setTimeoutPolicyEnabled((detail as any).timeoutPolicy.enabled || false);
            if ((detail as any).timeoutPolicy.enabled && (detail as any).timeoutPolicy.timeout) {
              setTimeoutValue((detail as any).timeoutPolicy.timeout);
            }
          }

          // 设置重试策略
          if ((detail as any).retryPolicy) {
            setRetryPolicyEnabled((detail as any).retryPolicy.enabled || false);
            if ((detail as any).retryPolicy.enabled) {
              if ((detail as any).retryPolicy.retryConditions) {
                const conditions = (detail as any).retryPolicy.retryConditions.split(',').map((s: string) => s.trim());
                setRetryConditions(conditions);
              }
              if ((detail as any).retryPolicy.numRetries) {
                setRetryAttempts((detail as any).retryPolicy.numRetries);
              }
            }
          }
        }
        // 无论认证是否开启，都获取消费者列表，确保回显能正常工作
        fetchConsumers();
        setLoading(false);
      },
      onError: (error) => {
        console.error('获取路由详情失败:', error);
        toast.error({
          message: '获取路由详情失败',
          duration: 5,
        });
        setLoading(false);
      }
    }
  );

  // 获取服务列表
  const { loading: serviceLoading, run: fetchServices } = useRequest(
    (serviceSource: string) => {
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      return getServicesBySource(instanceId as string, serviceSource, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        // 处理不同的响应结构情况
        let serviceData: any[] = [];

        // 如果是直接返回数组
        if (Array.isArray(res)) {
          serviceData = res;
        }
        // 如果是返回带有result字段的对象结构
        else if (res && res.result && Array.isArray(res.result)) {
          serviceData = res.result;
        }

        if (serviceData.length > 0) {
          const options = serviceData.map(item => ({
            label: item.serviceName,
            value: item.serviceName,
            namespace: item.namespace || '',
            clusterId: item.clusterId || ''
          }));
          setServiceOptions(options);
        } else {
          setServiceOptions([]);
        }
      },
      onError: (error) => {
        console.error('获取服务列表失败:', error);
        toast.error({
          message: '获取服务列表失败',
          duration: 5,
        });
        setServiceOptions([]);
      }
    }
  );

  // 获取消费者列表
  const { loading: consumerLoading, run: fetchConsumers } = useRequest(
    () => {
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      return getConsumerList(instanceId as string, {
        pageNo: 1,
        pageSize: 200, // 增加页大小，确保获取所有消费者
        orderBy: 'createTime',
        order: 'desc'
      }, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.success && res?.page?.result) {
          const consumerList = res.page.result;
          setAvailableConsumers(consumerList);
        } else {
          setAvailableConsumers([]);
        }
      },
      onError: (error) => {
        console.error('获取消费者列表失败:', error);
        toast.error({
          message: '获取消费者列表失败',
          duration: 5,
        });
        // 错误情况下清空消费者列表，但保留已选中的消费者名称
        setAvailableConsumers([]);
      }
    }
  );

  // 添加获取服务端口的函数
  const fetchServicePorts = async (clusterId: string, serviceName: string, namespace: string) => {
    try {
      setServicePortsLoading(true);
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      const res = await getServicePorts(clusterId, serviceName, namespace, instanceRegion);

      if (res?.success && res?.result) {
        // 处理端口数据并去重
        const portsData = res.result || [];
        // 提取端口号并去重
        const uniquePorts = Array.from(new Set(
          portsData.map(portInfo => {
            // 分割字符串，只取端口号部分
            return portInfo.split(' ')[0];
          })
        ));
        setServicePorts(uniquePorts);
      } else {
        setServicePorts([]);
      }
    } catch (error) {
      console.error('获取服务端口信息失败:', error);
      toast.error({
        message: '获取服务端口信息失败',
        duration: 5,
      });
      setServicePorts([]);
    } finally {
      setServicePortsLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (instanceId && routeName) {
      // 先获取实例详情
      fetchInstanceDetail();
    }
  }, [instanceId, routeName]);

  // 当实例详情加载完成后，获取路由详情
  useEffect(() => {
    if (instanceDetail?.region && instanceId && routeName) {
      fetchRouteDetail();
    }
  }, [instanceDetail?.region, instanceId, routeName]);

  // 当实例详情加载完成后，处理待获取的服务列表
  useEffect(() => {
    if (instanceDetail?.region && pendingServiceSource) {
      fetchServices(pendingServiceSource);
      setPendingServiceSource(''); // 清空待处理状态
    }
  }, [instanceDetail?.region, pendingServiceSource]);

  // 处理提交
  const handleSubmit = async () => {
    try {
      setSubmitting(true);

      // 验证路径重写配置
      if (rewriteEnabled && !rewritePath.trim()) {
        toast.error({
          message: '开启路径重写时，重写路径为必填项',
          duration: 5,
        });
        setSubmitting(false);
        return;
      }

      // 验证目标服务
      if (multiService) {
        // 多服务验证
        if (targetServices.length === 0) {
          toast.error({
            message: '请至少添加一个目标服务',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }

        // 验证每个服务的必填字段
        for (const service of targetServices) {
          if (!service.serviceName) {
            toast.error({
              message: '请选择所有服务的服务名称',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }

          if (!service.servicePort) {
            toast.error({
              message: '请选择所有服务的服务端口',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
        }

        // 验证比例分发的总和
        if (trafficDistributionStrategy === 'ratio') {
          const totalRatio = targetServices.reduce((sum, service) => sum + (service.requestRatio || 0), 0);
          if (totalRatio !== 100) {
            toast.error({
              message: '请求比例总和必须等于100%',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
        }

        // 验证模型名称分发的唯一性
        if (trafficDistributionStrategy === 'model_name') {
          const modelNames = targetServices.map(service => service.modelName).filter(Boolean);
          const uniqueModelNames = new Set(modelNames);
          if (modelNames.length !== uniqueModelNames.size) {
            toast.error({
              message: '模型名称不能重复',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }

          // 验证所有服务都有模型名称
          for (const service of targetServices) {
            if (!service.modelName) {
              toast.error({
                message: '请为所有服务设置模型名称',
                duration: 5,
              });
              setSubmitting(false);
              return;
            }
          }
        }
      } else {
        // 单服务验证
        if (!targetService.serviceName) {
          toast.error({
            message: '请选择服务名称',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }

        if (!targetService.servicePort) {
          toast.error({
            message: '请选择服务端口',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
      }

      // 验证Token限流配置
      if (tokenRateLimitEnabled) {
        if (rateLimitStrategies.length === 0) {
          toast.error({
            message: '开启Token限流后，请至少添加一个限流策略',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }

        // 验证每个限流策略的必填字段
        for (const strategy of rateLimitStrategies) {
          if (strategy.matchCondition.type === 'consumer' && !strategy.matchCondition.value) {
            toast.error({
              message: '请填写消费者名称',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }

          if (strategy.matchCondition.type === 'header' && (!strategy.matchCondition.key || !strategy.matchCondition.value)) {
            toast.error({
              message: '请填写完整的请求头信息',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }

          if (strategy.matchCondition.type === 'query_param' && (!strategy.matchCondition.key || !strategy.matchCondition.value)) {
            toast.error({
              message: '请填写完整的请求参数信息',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }

          if (!strategy.limitConfig.tokenAmount || strategy.limitConfig.tokenAmount <= 0) {
            toast.error({
              message: 'Token数量必须大于0',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
        }
      }

      // 验证超时策略配置
      if (timeoutPolicyEnabled) {
        if (!timeoutValue || timeoutValue <= 0) {
          toast.error({
            message: '超时时间必须大于0',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
      }

      // 验证重试策略配置
      if (retryPolicyEnabled) {
        if (!retryConditions || retryConditions.length === 0) {
          toast.error({
            message: '请至少选择一个重试条件',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }

        if (!retryAttempts || retryAttempts <= 0) {
          toast.error({
            message: '重试次数必须大于0',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
      }

      try {
        const values = await form.validateFields();

        // 过滤掉空的请求头和请求参数
        const filteredHeaders = headerParams
          .filter(item => item.key && item.value)
          .map(item => ({
            key: item.key.trim(),
            matchType: item.matchType || 'exact',
            value: item.value.trim()
          }));

        const filteredQueryParams = queryParams
          .filter(item => item.key && item.value)
          .map(item => ({
            key: item.key.trim(),
            matchType: item.matchType || 'exact',
            value: item.value.trim()
          }));

        // 构建请求数据
        const requestData: any = {
          matchRules: {
            pathRule: {
              matchType: values.pathMatchType,
              value: values.pathValue,
              caseSensitive: caseSensitive
            },
            methods: Array.isArray(values.methods) ? values.methods : [],
            headers: filteredHeaders,
            queryParams: filteredQueryParams
          },
          multiService: multiService,
          authEnabled: authEnabled,
          allowedConsumers: selectedConsumerNames
        };

        // 如果是 pom 产品的路由，添加 srcProduct 字段
        if (srcProduct === 'pom') {
          requestData.srcProduct = 'pom';
        }

        // 添加路径重写配置
        requestData.rewrite = {
          enabled: rewriteEnabled
        };

        if (rewriteEnabled && rewritePath.trim()) {
          requestData.rewrite.path = rewritePath.trim();
        }

        // 设置目标服务
        if (multiService) {
          requestData.trafficDistributionStrategy = trafficDistributionStrategy;
          requestData.targetService = targetServices.map(service => {
            const serviceData: any = {
              serviceSource: service.serviceSource,
              serviceName: service.serviceName,
              namespace: service.namespace,
              servicePort: Number(service.servicePort),
              loadBalanceAlgorithm: service.loadBalanceAlgorithm,
              ...(trafficDistributionStrategy === 'ratio'
                ? { requestRatio: service.requestRatio }
                : { modelName: service.modelName })
            };

            // 如果是哈希一致性算法，添加哈希配置
            if (service.loadBalanceAlgorithm === 'consistent-hash') {
              serviceData.hashType = service.hashType;
              // 当hashType为'ip'时传空字符串，其他情况传具体值
              if (service.hashType === 'ip') {
                serviceData.hashKey = '';
              } else {
                serviceData.hashKey = service.hashKey || '';
              }
            }

            return serviceData;
          });
        } else {
          const serviceData: any = {
            serviceSource: targetService.serviceSource,
            serviceName: targetService.serviceName,
            namespace: targetService.namespace,
            servicePort: Number(targetService.servicePort),
            loadBalanceAlgorithm: targetService.loadBalanceAlgorithm
          };

          // 如果是哈希一致性算法，添加哈希配置
          if (targetService.loadBalanceAlgorithm === 'consistent-hash') {
            serviceData.hashType = targetService.hashType;
            // 当hashType为'ip'时传空字符串，其他情况传具体值
            if (targetService.hashType === 'ip') {
              serviceData.hashKey = '';
            } else {
              serviceData.hashKey = targetService.hashKey || '';
            }
          }

          requestData.targetService = serviceData;
        }

        // 设置Token限流
        if (tokenRateLimitEnabled) {
          requestData.tokenRateLimit = {
            enabled: true,
            rule_items: rateLimitStrategies.map(strategy => ({
              match_condition: {
                type: strategy.matchCondition.type,
                key: strategy.matchCondition.type === 'consumer' ? '' : strategy.matchCondition.key,
                value: strategy.matchCondition.value
              },
              limit_config: {
                time_unit: strategy.limitConfig.timeUnit,
                token_amount: strategy.limitConfig.tokenAmount
              }
            }))
          };
        } else {
          requestData.tokenRateLimit = {
            enabled: false
          };
        }

        // 设置超时策略
        requestData.timeoutPolicy = {
          enabled: timeoutPolicyEnabled
        };

        if (timeoutPolicyEnabled) {
          requestData.timeoutPolicy.timeout = timeoutValue;
        }

        // 设置重试策略
        requestData.retryPolicy = {
          enabled: retryPolicyEnabled
        };

        if (retryPolicyEnabled) {
          requestData.retryPolicy.retryConditions = retryConditions.join(',');
          requestData.retryPolicy.numRetries = retryAttempts;
        }



        // 调用更新路由接口
        // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
        const instanceRegion = instanceDetail?.region || region;
        const res = await updateRoute(
          instanceId as string,
          routeName as string,
          requestData,
          instanceRegion
        );

        if (res && res.success) {
          toast.success({
            message: '路由更新成功',
            duration: 5,
          });
          goBack(); // 返回列表页
        } else {
          // 显示错误信息
          let errorMsg = '更新路由失败，请重试';

          // 解析复杂错误结构
          if (res?.message) {
            // 如果message是对象
            if (typeof res.message === 'object' && res.message !== null) {
              // 检查是否包含global字段
              const msgObj = res.message as any;
              if (msgObj.global) {
                errorMsg = msgObj.global;
              }
            }
            // 如果message是字符串
            else if (typeof res.message === 'string') {
              errorMsg = res.message;
            }
          }

          // 显示特定的错误代码
          const resWithCode = res as any;
          if (resWithCode?.code) {
            errorMsg = `${errorMsg} (${resWithCode.code})`;
          }

          console.error('更新路由失败:', res);
          toast.error({
            message: errorMsg,
            duration: 5,
          });
        }
      } catch (error: any) {
        console.error('更新路由失败:', error);
        let errorMessage = '表单验证失败或更新过程中出现错误';

        if (error?.response?.data) {
          const errorData = error.response.data;

          // 尝试从错误响应中获取详细信息
          if (errorData.message) {
            if (typeof errorData.message === 'object' && errorData.message !== null) {
              const msgObj = errorData.message as any;
              if (msgObj.global) {
                errorMessage = msgObj.global;
              }
            } else if (typeof errorData.message === 'string') {
              errorMessage = errorData.message;
            }
          }

          if (errorData.code) {
            errorMessage = `${errorMessage} (${errorData.code})`;
          }
        } else if (error?.message && typeof error.message === 'string') {
          errorMessage = error.message;
        }

        toast.error({
          message: errorMessage,
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('更新路由失败:', error);
      let errorMessage = '表单验证失败或更新过程中出现错误';
      if (error?.message && typeof error.message === 'string') {
        errorMessage = error.message;
      }
      toast.error({
        message: errorMessage,
        duration: 5,
      });
    } finally {
      setSubmitting(false);
    }
  };

  // 返回列表
  const goBack = () => {
    // 返回实例详情页的路由配置标签
    navigate(`/instance/base/info?instanceId=${instanceId}&activeMenu=route-config`);
  };

  // 处理取消
  const handleCancel = () => {
    Modal.confirm({
      title: '确认取消',
      content: '取消编辑将丢失已修改的数据，确认要取消吗？',
      onOk: goBack,
      okText: '确认',
      cancelText: '取消'
    });
  };

  // 添加请求头参数
  const handleAddHeaderParam = () => {
    const newHeader = { key: '', matchType: '', value: '' };

    setHeaderParams([...headerParams, newHeader]);
  };

  // 删除请求头参数
  const handleRemoveHeaderParam = (index: number) => {

    const newHeaders = [...headerParams];
    newHeaders.splice(index, 1);
    setHeaderParams(newHeaders);
  };

  // 更新请求头参数
  const handleUpdateHeaderParam = (index: number, field: string, value: string) => {

    const newHeaders = [...headerParams];
    newHeaders[index] = { ...newHeaders[index], [field]: value };
    setHeaderParams(newHeaders);
  };

  // 添加请求参数
  const handleAddQueryParam = () => {
    setQueryParams([...queryParams, { key: '', matchType: '', value: '' }]);
  };

  // 删除请求参数
  const handleRemoveQueryParam = (index: number) => {
    const newParams = [...queryParams];
    newParams.splice(index, 1);
    setQueryParams(newParams);
  };

  // 更新请求参数
  const handleUpdateQueryParam = (index: number, field: string, value: string) => {
    const newParams = [...queryParams];
    newParams[index] = { ...newParams[index], [field]: value };
    setQueryParams(newParams);
  };

  // 处理服务来源变化
  const handleServiceSourceChange = (value: string) => {
    setTargetService({
      ...targetService,
      serviceSource: value,
      serviceName: '',
      namespace: ''
    });
    fetchServices(value);
  };

  // 处理服务名称变化
  const handleServiceNameChange = (value: string) => {
    const selected = serviceOptions.find(option => option.value === value);
    if (selected) {
      setTargetService({
        ...targetService,
        serviceName: value,
        namespace: selected.namespace,
        servicePort: null // 重置服务端口
      });

      // 如果有选中服务，则获取服务端口
      if (selected.clusterId && value && selected.namespace) {
        fetchServicePorts(selected.clusterId, value, selected.namespace);
      } else {
        setServicePorts([]);
      }
    }
  };

  // 处理服务端口变化
  const handleServicePortChange = (value: string) => {
    setTargetService({
      ...targetService,
      servicePort: parseInt(value, 10) || null
    });
  };



  // 处理认证开关变化
  const handleAuthEnabledChange = (checked: boolean) => {
    setAuthEnabled(checked);
    // 如果消费者列表还没有加载，此时加载，但不需要清除已选择的消费者
    if (availableConsumers.length === 0) {
      fetchConsumers();
    }
  };

  // 处理选择消费者变化
  const handleConsumerChange = (targetKeys: string[]) => {
    setSelectedConsumerNames(targetKeys);
  };

  // 多服务相关处理函数
  const handleMultiServiceChange = (checked: boolean) => {
    setMultiService(checked);
    if (checked && targetServices.length === 0) {
      // 初始化多服务列表，保持哈希配置
      setTargetServices([{
        key: '0',
        serviceSource: 'CCE',
        serviceName: '',
        namespace: '',
        servicePort: null,
        loadBalanceAlgorithm: 'round-robin',
        hashType: undefined,
        hashKey: undefined,
        requestRatio: 100
      }]);
    }
  };

  const handleTrafficDistributionStrategyChange = (value: 'ratio' | 'model_name') => {
    setTrafficDistributionStrategy(value);
    // 重置服务列表的相关字段，但保持哈希配置
    const updatedServices = targetServices.map(service => {
      const newService = { ...service };
      if (value === 'ratio') {
        delete newService.modelName;
        if (!newService.requestRatio) {
          newService.requestRatio = Math.floor(100 / targetServices.length);
        }
      } else {
        delete newService.requestRatio;
        if (!newService.modelName) {
          newService.modelName = '';
        }
      }
      // 保持哈希配置
      newService.hashType = service.hashType;
      newService.hashKey = service.hashKey;
      return newService;
    });
    setTargetServices(updatedServices);
  };

  const handleAddTargetService = () => {
    const newService: ServiceType = {
      key: Date.now().toString(),
      serviceSource: 'CCE',
      serviceName: '',
      namespace: '',
      servicePort: null,
      loadBalanceAlgorithm: 'round-robin',
      hashType: undefined,
      hashKey: undefined,
      ...(trafficDistributionStrategy === 'ratio'
        ? { requestRatio: 0 }
        : { modelName: '' })
    };
    setTargetServices([...targetServices, newService]);
  };

  const handleRemoveTargetService = (key: string) => {
    const newServices = targetServices.filter(service => service.key !== key);
    setTargetServices(newServices);
  };

  const handleUpdateTargetService = (key: string, field: string, value: any) => {
    const newServices = targetServices.map(service => {
      if (service.key === key) {
        const updatedService = { ...service, [field]: value };

        // 如果是服务名称变化，需要更新命名空间和重置端口
        if (field === 'serviceName') {
          const selected = serviceOptions.find(option => option.value === value);
          if (selected) {
            updatedService.namespace = selected.namespace;
            updatedService.servicePort = null;
          }
        }

        return updatedService;
      }
      return service;
    });
    setTargetServices(newServices);
  };

  // Token限流相关处理函数
  const handleTokenRateLimitEnabledChange = (checked: boolean) => {
    setTokenRateLimitEnabled(checked);
    if (checked && rateLimitStrategies.length === 0) {
      // 初始化限流策略
      setRateLimitStrategies([{
        key: '0',
        matchCondition: {
          type: 'consumer',
          key: '',
          value: ''
        },
        limitConfig: {
          timeUnit: 'minute',
          tokenAmount: 1000
        }
      }]);
    } else if (!checked) {
      // 关闭时清空限流策略
      setRateLimitStrategies([]);
    }
  };

  const handleAddRateLimitStrategy = () => {
    const newStrategy = {
      key: Date.now().toString(),
      matchCondition: {
        type: 'consumer' as const,
        key: '',
        value: ''
      },
      limitConfig: {
        timeUnit: 'minute' as const,
        tokenAmount: 1000
      }
    };
    setRateLimitStrategies([...rateLimitStrategies, newStrategy]);
  };

  const handleRemoveRateLimitStrategy = (key: string) => {
    const newStrategies = rateLimitStrategies.filter(strategy => strategy.key !== key);
    setRateLimitStrategies(newStrategies);
  };

  const handleUpdateRateLimitStrategy = (key: string, field: string, value: any) => {
    const newStrategies = rateLimitStrategies.map(strategy => {
      if (strategy.key === key) {
        const updatedStrategy = { ...strategy };

        if (field === 'matchCondition.type') {
          // 切换类型时重置相关字段
          updatedStrategy.matchCondition = {
            type: value,
            key: value === 'consumer' ? '' : '',
            value: ''
          };
        } else if (field.startsWith('matchCondition.')) {
          const fieldName = field.split('.')[1];
          updatedStrategy.matchCondition = {
            ...updatedStrategy.matchCondition,
            [fieldName]: value
          };
        } else if (field.startsWith('limitConfig.')) {
          const fieldName = field.split('.')[1];
          updatedStrategy.limitConfig = {
            ...updatedStrategy.limitConfig,
            [fieldName]: value
          };
        }

        return updatedStrategy;
      }
      return strategy;
    });
    setRateLimitStrategies(newStrategies);
  };

  // 计算比例总和
  const calculateTotalRatio = () => {
    return targetServices.reduce((sum, service) => sum + (service.requestRatio || 0), 0);
  };

  // 验证比例是否有效
  const isRatioValid = () => {
    return calculateTotalRatio() === 100;
  };

  // 处理路径重写开关变化
  const handleRewriteEnabledChange = (checked: boolean) => {

    setRewriteEnabled(checked);
    if (!checked) {
      // 关闭重写时，清空重写路径
      setRewritePath('');
      setRewriteMode('standard');
    }
  };

  // 处理重写路径变化
  const handleRewritePathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setRewritePath(value);
  };

  // 添加负载均衡算法弹窗处理函数
  const handleOpenLoadBalanceModal = (service: ServiceType, isMultiService: boolean) => {
    setCurrentEditService({
      key: service.key,
      serviceName: service.serviceName,
      loadBalanceAlgorithm: service.loadBalanceAlgorithm,
      hashType: service.hashType,
      hashKey: service.hashKey,
      isMultiService
    });
    setModalFormErrors({});
    setLoadBalanceModalVisible(true);
  };

  const handleLoadBalanceModalOk = () => {
    if (!currentEditService) return;

    // 表单验证
    const errors: ModalFormErrorsType = {};

    // 验证负载均衡算法
    if (!currentEditService.loadBalanceAlgorithm) {
      errors.loadBalanceAlgorithm = '请选择负载均衡算法';
    }

    // 如果选择哈希一致性，验证相关字段
    if (currentEditService.loadBalanceAlgorithm === 'consistent-hash') {
      if (!currentEditService.hashType) {
        errors.hashType = '请选择 Hash 方式';
      } else {
        // 验证需要输入key的Hash方式
        const needsKey = ['header', 'query_param', 'cookie'].includes(currentEditService.hashType);
        if (needsKey && (!currentEditService.hashKey || currentEditService.hashKey.trim() === '')) {
          errors.hashKey = '请输入对应的值';
        }
      }
    }

    if (Object.keys(errors).length > 0) {
      setModalFormErrors(errors);
      return;
    }

    if (currentEditService.isMultiService) {
      // 更新多服务
      const services = targetServices.map(service => {
        if (service.key === currentEditService.key) {
          return {
            ...service,
            loadBalanceAlgorithm: currentEditService.loadBalanceAlgorithm,
            hashType: currentEditService.hashType,
            hashKey: currentEditService.hashKey
          };
        }
        return service;
      });
      setTargetServices(services);
    } else {
      // 更新单服务
      setTargetService({
        ...targetService,
        loadBalanceAlgorithm: currentEditService.loadBalanceAlgorithm,
        hashType: currentEditService.hashType,
        hashKey: currentEditService.hashKey
      });
    }

    setLoadBalanceModalVisible(false);
    setCurrentEditService(null);
    setModalFormErrors({});
  };

  const handleLoadBalanceModalCancel = () => {
    setLoadBalanceModalVisible(false);
    setCurrentEditService(null);
    setModalFormErrors({});
  };

  const handleModalLoadBalanceChange = (value: string) => {
    if (currentEditService) {
      setCurrentEditService({
        ...currentEditService,
        loadBalanceAlgorithm: value,
        // 当不是哈希一致性时，清空哈希相关配置
        hashType: value === 'consistent-hash' ? currentEditService.hashType : undefined,
        hashKey: value === 'consistent-hash' ? currentEditService.hashKey : undefined,
      });

      // 清除负载均衡算法的错误状态
      if (modalFormErrors.loadBalanceAlgorithm) {
        setModalFormErrors({
          ...modalFormErrors,
          loadBalanceAlgorithm: undefined
        });
      }
    }
  };

  const handleHashTypeChange = (value: string) => {
    if (currentEditService) {
      setCurrentEditService({
        ...currentEditService,
        hashType: value,
        hashKey: value === 'ip' ? undefined : currentEditService.hashKey
      });

      // 清除hash类型的错误状态
      if (modalFormErrors.hashType) {
        setModalFormErrors({
          ...modalFormErrors,
          hashType: undefined
        });
      }
    }
  };

  const handleHashKeyChange = (value: string) => {
    if (currentEditService) {
      setCurrentEditService({
        ...currentEditService,
        hashKey: value
      });

      // 清除hashKey的错误状态
      if (modalFormErrors.hashKey && value.trim() !== '') {
        setModalFormErrors({
          ...modalFormErrors,
          hashKey: undefined
        });
      }
    }
  };

  // 格式化负载均衡Tooltip内容
  const formatLoadBalanceTooltip = (service: ServiceType) => {
    const algorithmLabels = {
      'round-robin': '轮询',
      'random': '随机',
      'least-conn': '最小连接数',
      'consistent-hash': '哈希一致性'
    };

    const hashTypeLabels = {
      'header': '基于Header',
      'query_param': '基于请求参数',
      'ip': '基于源IP',
      'cookie': '基于Cookie'
    };

    const lines = [algorithmLabels[service.loadBalanceAlgorithm] || service.loadBalanceAlgorithm];

    if (service.loadBalanceAlgorithm === 'consistent-hash' && service.hashType) {
      lines.push(`Hash 方式：${hashTypeLabels[service.hashType] || service.hashType}`);

      // 如果有hashKey，显示具体的key值
      if (service.hashKey && ['header', 'query_param', 'cookie'].includes(service.hashType)) {
        const keyLabels = {
          'header': '请求头',
          'query_param': '请求参数',
          'cookie': 'Cookie名称'
        };
        lines.push(`${keyLabels[service.hashType]}：${service.hashKey}`);
      }
    }

    return (
      <div>
        {lines.map((line, index) => (
          <div key={index}>{line}</div>
        ))}
      </div>
    );
  };

  return (
    <div className={styles['create-instance-wrap']}>
      <div className={styles['create-instance-header']}>
        <Button
          type="text"
          onClick={goBack}
          icon={<OutlinedLeft />}
          style={{ marginRight: '8px', color: '#83868c', fontSize: '14px' }}
        >
          返回
        </Button>
        <span style={{ fontSize: '16px', fontWeight: '500', color: '#151b26' }}>编辑路由</span>
      </div>
      <div className={styles['create-instance-content-wrap']}>
        <div className={styles['create-instance-content']}>
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
              <Loading loading={true} size="large" tip="加载中..." />
            </div>
          ) : (
            <Form
              form={form}
              autoComplete="off"
              labelAlign="left"
              labelWidth={75} // 设置label宽度,表单全局生效
              wrapperCol={{ style: { flex: '1', width: 'auto' } }}
            >
              {/* 基础配置模块 */}
              <div className={`${styles["module-container"]} ${styles["basic-info-module"]}`}>
                <div className={styles["module-title"]}>基础配置</div>
                <div className={styles["module-content"]}>
                  <Form.Item
                    label="路由名称"
                  >
                    <span>{routeNameValue}</span>
                  </Form.Item>
                </div>
              </div>

              {/* 路由规则模块 */}
              <div className={`${styles["module-container"]} ${styles["route-config-module"]}`}>
                <div className={styles["module-title"]}>路由规则</div>
                <div className={styles["module-content"]}>
                  <Form.Item label="匹配规则" required style={{ marginBottom: 16 }}>
                    <div style={{
                      borderRadius: '6px',
                      padding: '16px',
                      display: 'inline-block',
                      background: '#F7F7F9',
                      width: '100%'
                    }}>
                      <Form.Item
                        label="路径（Path）"
                        style={{ marginBottom: 16 }}
                        labelCol={{ span: 4 }}
                        required
                      >
                        <Form.Item
                          name="pathValue"
                          noStyle
                          rules={[{ required: true, message: '请输入路径匹配值' }]}
                        >
                          <Input
                            placeholder="请输入路径匹配值，如 /user"
                            style={{ width: '360px !important' }}
                            className={styles.pathInput}
                            addonBefore={
                              <Form.Item
                                name="pathMatchType"
                                noStyle
                                rules={[{ required: true, message: '请选择路径匹配规则' }]}
                              >
                                <Select
                                  className={styles.pathSelector}
                                >
                                  {matchTypeOptions.map(option => (
                                    <Option key={option.value} value={option.value}>
                                      {option.label}
                                    </Option>
                                  ))}
                                </Select>
                              </Form.Item>
                            }
                          />
                        </Form.Item>
                      </Form.Item>

                      <Form.Item
                        label="请求方法（Method）"
                        name="methods"
                        style={{ marginBottom: 16 }}
                        labelCol={{ span: 4 }}
                        rules={[{ required: true, message: '请选择请求方法' }]}
                      >
                        <Select
                          mode="multiple"
                          placeholder="请选择请求方法"
                          style={{ width: 360 }}
                          options={httpMethodOptions}
                          getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
                          listHeight={200}
                          tagRender={({ label, closable, onClose }) => (
                            <div style={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              padding: '2px 8px',
                              marginRight: '4px',
                              backgroundColor: '#f5f5f5',
                              borderRadius: '4px',
                              fontSize: '14px'
                            }}>
                              {label}
                              {closable && (
                                <span
                                  style={{ marginLeft: '4px', cursor: 'pointer' }}
                                  onClick={onClose}
                                >
                                  ×
                                </span>
                              )}
                            </div>
                          )}
                        />
                      </Form.Item>

                      <Form.Item
                        label="请求头（Header）"
                        style={{ marginBottom: 16 }}
                        labelCol={{ span: 4 }}
                      >
                        <div style={{ width: '100%' }}>
                          {headerParams.map((header, index) => (
                            <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: 8, gap: 8, flexWrap: 'nowrap', whiteSpace: 'nowrap' }}>
                              <Input
                                placeholder="请输入 Header Key"
                                value={header.key}
                                style={{ width: '220px !important', flex: '0 0 220px' }}
                                onChange={(e) => handleUpdateHeaderParam(index, 'key', e.target.value)}
                                onBlur={(e) => {
                                  // 确保在失去焦点时更新headerParams
                                  const newHeaders = [...headerParams];
                                  newHeaders[index] = { ...newHeaders[index], key: e.target.value.trim() };
                                  setHeaderParams(newHeaders);
                                }}
                              />
                              <Select
                                value={header.matchType ? header.matchType : undefined}
                                onChange={(value) => handleUpdateHeaderParam(index, 'matchType', value)}
                                placeholder="请选择匹配规则"
                                style={{ width: '180px !important', flex: '0 0 180px' }}
                                allowClear
                              >
                                {matchTypeOptions.map(option => (
                                  <Option key={option.value} value={option.value}>
                                    {option.label}
                                  </Option>
                                ))}
                              </Select>
                              <Input
                                placeholder="请输入 Header 值"
                                value={header.value}
                                style={{ width: '220px !important', flex: '0 0 220px' }}
                                onChange={(e) => handleUpdateHeaderParam(index, 'value', e.target.value)}
                                onBlur={(e) => {
                                  // 确保在失去焦点时更新headerParams
                                  const newHeaders = [...headerParams];
                                  newHeaders[index] = { ...newHeaders[index], value: e.target.value.trim() };
                                  setHeaderParams(newHeaders);
                                }}
                              />
                              <span
                                style={{ color: '#2468F2', marginLeft: '8px', cursor: 'pointer' }}
                                onClick={() => handleRemoveHeaderParam(index)}
                              >
                                删除
                              </span>
                            </div>
                          ))}
                          <Button
                            type="text"
                            icon={<OutlinedPlus />}
                            onClick={handleAddHeaderParam}
                            style={{ paddingLeft: 0, color: '#2468F2' }}
                          >
                            添加请求头
                          </Button>
                        </div>
                      </Form.Item>

                      <Form.Item
                        label="请求参数（Query）"
                        style={{ marginBottom: 0 }}
                        labelCol={{ span: 4 }}
                      >
                        <div style={{ width: '100%' }}>
                          {queryParams.map((param, index) => (
                            <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: 8, gap: 8, flexWrap: 'nowrap', whiteSpace: 'nowrap' }}>
                              <Input
                                placeholder="请输入 Query Key"
                                value={param.key}
                                style={{ width: '220px !important', flex: '0 0 220px' }}
                                onChange={(e) => handleUpdateQueryParam(index, 'key', e.target.value)}
                              />
                              <Select
                                value={param.matchType ? param.matchType : undefined}
                                onChange={(value) => handleUpdateQueryParam(index, 'matchType', value)}
                                placeholder="请选择匹配规则"
                                style={{ width: '180px !important', flex: '0 0 180px' }}
                                allowClear
                              >
                                {matchTypeOptions.map(option => (
                                  <Option key={option.value} value={option.value}>
                                    {option.label}
                                  </Option>
                                ))}
                              </Select>
                              <Input
                                placeholder="请输入 Query 值"
                                value={param.value}
                                style={{ width: '220px !important', flex: '0 0 220px' }}
                                onChange={(e) => handleUpdateQueryParam(index, 'value', e.target.value)}
                              />
                              <span
                                style={{ color: '#2468F2', marginLeft: '8px', cursor: 'pointer' }}
                                onClick={() => handleRemoveQueryParam(index)}
                              >
                                删除
                              </span>
                            </div>
                          ))}
                          <Button
                            type="text"
                            icon={<OutlinedPlus />}
                            onClick={handleAddQueryParam}
                            style={{ paddingLeft: 0, color: '#2468F2' }}
                          >
                            添加请求参数
                          </Button>
                        </div>
                      </Form.Item>
                    </div>
                  </Form.Item>
                  <Form.Item label="路径重写">
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', paddingTop: 8 }}>
                      <Checkbox
                        checked={rewriteEnabled}
                        onChange={(e) => handleRewriteEnabledChange(e.target.checked)}
                      >
                        开启路径重写
                      </Checkbox>
                      <div style={{ marginTop: 4, color: '#84878c', fontSize: 12 }}>
                        开启后，需配置重写路径，将覆盖重写原路径匹配值
                      </div>
                    </div>
                  </Form.Item>

                  {rewriteEnabled && (
                    <>
                      <Form.Item
                        label="重写模式"
                        required
                        style={{ margin: '16px 0 16px 0' }}
                        tooltip="标准模式：对于路径精确匹配的路由，进行完整覆盖重写；对于路径前缀匹配的路由，仅重写前缀匹配的部分"
                      >
                        <Radio.Group
                          value={rewriteMode}
                          onChange={(e: any) => setRewriteMode(e.target.value)}
                        >
                          <Radio.Button value="standard">标准</Radio.Button>
                        </Radio.Group>
                      </Form.Item>

                      <Form.Item
                        label="重写路径"
                        required
                        style={{ marginBottom: 0 }}
                      >
                        <Input
                          value={rewritePath}
                          onChange={handleRewritePathChange}
                          placeholder="请输入重写路径，如/default"
                          style={{ width: 360 }}
                        />
                      </Form.Item>
                    </>
                  )}
                </div>
              </div>

              {/* 目标服务模块 */}
              <div className={`${styles["module-container"]}`}>
                <div className={styles["module-title"]}>目标服务</div>
                <div className={styles["module-content"]}>
                  <Form.Item label="服务模式" required>
                    <div style={{ marginBottom: 16 }}>
                      <Radio.Group
                        value={multiService ? 'multi' : 'single'}
                        onChange={(e) => handleMultiServiceChange((e.target as HTMLInputElement)?.value === 'multi')}
                      >
                        <Radio.Button value="single">单服务</Radio.Button>
                        <Radio.Button value="multi">多服务</Radio.Button>
                      </Radio.Group>
                    </div>
                  </Form.Item>

                  {multiService && (
                    <Form.Item label="流量策略" required>
                      <div style={{ marginBottom: 16 }}>
                        <Radio.Group
                          value={trafficDistributionStrategy}
                          onChange={(e) => handleTrafficDistributionStrategyChange((e.target as HTMLInputElement)?.value as 'ratio' | 'model_name')}
                        >
                          <Radio.Button value="ratio">按比例</Radio.Button>
                          <Radio.Button value="model_name">按模型名称</Radio.Button>
                        </Radio.Group>
                      </div>
                    </Form.Item>
                  )}

                  <Form.Item
                    label="目标服务"
                    required
                    validateStatus={multiService && trafficDistributionStrategy === 'ratio' && !isRatioValid() ? 'error' : ''}
                    help={multiService && trafficDistributionStrategy === 'ratio' && !isRatioValid() ?
                      `请求比例总和必须为100%，当前为${calculateTotalRatio()}%` : ''}
                  >
                    {multiService ? (
                      <div>
                        <Table
                          style={{ width: '100%' }}
                          columns={[
                            {
                              title: '服务来源',
                              dataIndex: 'serviceSource',
                              key: 'serviceSource',
                              render: (_, record) => (
                                <Select
                                  value={record.serviceSource}
                                  onChange={(value) => handleUpdateTargetService(record.key, 'serviceSource', value)}
                                  style={{ width: '100%' }}
                                >
                                  <Option value="CCE">容器引擎 CCE</Option>
                                </Select>
                              )
                            },
                            {
                              title: '服务名称',
                              dataIndex: 'serviceName',
                              key: 'serviceName',
                              render: (_, record) => (
                                <Select
                                  value={record.serviceName || undefined}
                                  onChange={(value) => handleUpdateTargetService(record.key, 'serviceName', value)}
                                  style={{ width: '100%' }}
                                  placeholder="请选择服务"
                                  loading={serviceLoading}
                                  disabled={!record.serviceSource}
                                  showSearch
                                  optionFilterProp="label"
                                  filterOption={(input, option) =>
                                    (option?.label?.toString().toLowerCase() || '').includes(input.toLowerCase())
                                  }
                                >
                                  {serviceOptions.map(option => (
                                    <Option
                                      key={option.value}
                                      value={option.value}
                                      label={option.label}
                                    >
                                      <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                                        <span style={{ fontWeight: 'normal' }}>{option.value}</span>
                                        <span style={{ color: '#8c8c8c', fontSize: '12px', marginLeft: '8px' }}>
                                          （{option.namespace}）
                                        </span>
                                      </div>
                                    </Option>
                                  ))}
                                </Select>
                              )
                            },
                            {
                              title: '服务端口',
                              dataIndex: 'servicePort',
                              key: 'servicePort',
                              render: (_, record) => (
                                <Select
                                  value={record.servicePort ? record.servicePort.toString() : undefined}
                                  onChange={(value) => handleUpdateTargetService(record.key, 'servicePort', parseInt(value, 10))}
                                  placeholder="请选择端口"
                                  style={{ width: '100%' }}
                                  disabled={!record.serviceName}
                                  onFocus={() => {
                                    // 选择框获得焦点时重新获取服务端口信息
                                    if (record.serviceName) {
                                      const selected = serviceOptions.find(option =>
                                        option.value === record.serviceName &&
                                        option.namespace === record.namespace
                                      );
                                      if (selected?.clusterId) {
                                        fetchServicePorts(selected.clusterId, record.serviceName, record.namespace);
                                      }
                                    }
                                  }}
                                  onDropdownVisibleChange={(open) => {
                                    // 下拉菜单打开时重新获取服务端口信息
                                    if (open && record.serviceName) {
                                      const selected = serviceOptions.find(option =>
                                        option.value === record.serviceName &&
                                        option.namespace === record.namespace
                                      );
                                      if (selected?.clusterId) {
                                        fetchServicePorts(selected.clusterId, record.serviceName, record.namespace);
                                      }
                                    }
                                  }}
                                >
                                  {servicePorts.map(port => (
                                    <Option key={port} value={port}>
                                      {port}
                                    </Option>
                                  ))}
                                </Select>
                              )
                            },
                            ...(trafficDistributionStrategy === 'ratio' ? [{
                              title: '请求比例（%）',
                              dataIndex: 'requestRatio',
                              key: 'requestRatio',
                              render: (_, record) => (
                                <div style={{ display: 'flex', alignItems: 'center', height: '100%', backgroundColor: '#ffffff', borderRadius: '4px' }}>
                                  <InputNumber
                                    value={record.requestRatio || 0}
                                    onChange={(value) => handleUpdateTargetService(record.key, 'requestRatio', value || 0)}
                                    style={{ width: '100%' }}
                                    min={0}
                                    max={100}
                                    placeholder="0"
                                  />
                                </div>
                              )
                            }] : [{
                              title: '模型名称',
                              dataIndex: 'modelName',
                              key: 'modelName',
                              render: (_, record) => (
                                <Input
                                  value={record.modelName || ''}
                                  onChange={(e) => handleUpdateTargetService(record.key, 'modelName', e.target.value)}
                                  style={{ width: '100%' }}
                                  placeholder="请输入模型名称"
                                />
                              )
                            }]),
                            {
                              title: '操作',
                              key: 'action',
                              width: '150px',
                              render: (_, record) => (
                                <Space>
                                  <Tooltip
                                    title={record.serviceName ? formatLoadBalanceTooltip(record) : '请先选择服务'}
                                    placement="top"
                                  >
                                    <Button
                                      type="text"
                                      onClick={() => handleOpenLoadBalanceModal(record, true)}
                                      style={{
                                        color: record.serviceName ? '#2468F2' : '#B8BABF',
                                        padding: 0
                                      }}
                                      disabled={!record.serviceName}
                                    >
                                      负载均衡
                                    </Button>
                                  </Tooltip>
                                  <Button
                                    type="text"
                                    onClick={() => handleRemoveTargetService(record.key)}
                                    className={styles.deleteButton}
                                    disabled={targetServices.length <= 1}
                                  >
                                    删除
                                  </Button>
                                </Space>
                              )
                            }
                          ]}
                          dataSource={targetServices}
                          pagination={false}
                          rowKey="key"
                        />
                        <Button
                          type="text"
                          icon={<OutlinedPlus />}
                          onClick={handleAddTargetService}
                          style={{ marginTop: 16, color: '#2468F2', paddingLeft: 0 }}
                        >
                          添加服务
                        </Button>

                      </div>
                    ) : (
                      <Table
                        style={{ width: '100%' }}
                        columns={[
                          {
                            title: '服务来源',
                            dataIndex: 'serviceSource',
                            key: 'serviceSource',
                            render: () => (
                              <Select
                                value={targetService.serviceSource}
                                onChange={handleServiceSourceChange}
                                style={{ width: '100%' }}
                              >
                                <Option value="CCE">容器引擎 CCE</Option>
                              </Select>
                            )
                          },
                          {
                            title: '服务名称',
                            dataIndex: 'serviceName',
                            key: 'serviceName',
                            render: () => (
                              <Select
                                value={targetService.serviceName || undefined}
                                onChange={handleServiceNameChange}
                                style={{ width: '100%' }}
                                placeholder="请选择服务"
                                loading={serviceLoading}
                                disabled={!targetService.serviceSource}
                                showSearch
                                optionFilterProp="label"
                                filterOption={(input, option) =>
                                  (option?.label?.toString().toLowerCase() || '').includes(input.toLowerCase())
                                }
                                onFocus={() => {
                                  if (targetService.serviceSource && serviceOptions.length === 0) {
                                    fetchServices(targetService.serviceSource);
                                  }
                                }}
                                onDropdownVisibleChange={(open) => {
                                  if (open && targetService.serviceSource && serviceOptions.length === 0) {
                                    fetchServices(targetService.serviceSource);
                                  }
                                }}
                                dropdownRender={menu => (
                                  <>
                                    {menu}
                                    {serviceOptions.length === 0 && !serviceLoading && (
                                      <div style={{ padding: '8px', textAlign: 'center', color: '#8c8c8c' }}>
                                        暂无服务数据
                                      </div>
                                    )}
                                  </>
                                )}
                              >
                                {serviceOptions.map(option => (
                                  <Option
                                    key={option.value}
                                    value={option.value}
                                    label={option.label}
                                  >
                                    <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                                      <span style={{ fontWeight: 'normal' }}>{option.value}</span>
                                      <span style={{ color: '#8c8c8c', fontSize: '12px', marginLeft: '8px' }}>
                                        （{option.namespace}）
                                      </span>
                                    </div>
                                  </Option>
                                ))}
                              </Select>
                            )
                          },
                          {
                            title: '服务端口',
                            dataIndex: 'servicePort',
                            key: 'servicePort',
                            render: () => (
                              <Select
                                value={targetService.servicePort ? targetService.servicePort.toString() : undefined}
                                onChange={handleServicePortChange}
                                placeholder="请选择服务端口"
                                style={{ width: '100%' }}
                                loading={servicePortsLoading}
                                disabled={!targetService.serviceName}
                                onFocus={() => {
                                  // 选择框获得焦点时重新获取服务端口信息
                                  if (targetService.serviceName) {
                                    const selected = serviceOptions.find(option =>
                                      option.value === targetService.serviceName &&
                                      option.namespace === targetService.namespace
                                    );
                                    if (selected?.clusterId) {
                                      fetchServicePorts(selected.clusterId, targetService.serviceName, targetService.namespace);
                                    }
                                  }
                                }}
                                onDropdownVisibleChange={(open) => {
                                  // 下拉菜单打开时重新获取服务端口信息
                                  if (open && targetService.serviceName) {
                                    const selected = serviceOptions.find(option =>
                                      option.value === targetService.serviceName &&
                                      option.namespace === targetService.namespace
                                    );
                                    if (selected?.clusterId) {
                                      fetchServicePorts(selected.clusterId, targetService.serviceName, targetService.namespace);
                                    }
                                  }
                                }}
                              >
                                {servicePorts.map(port => (
                                  <Option key={port} value={port}>
                                    {port}
                                  </Option>
                                ))}
                              </Select>
                            )
                          },
                          {
                            title: '操作',
                            key: 'action',
                            width: '100px',
                            render: () => (
                              <Tooltip
                                title={targetService.serviceName ? formatLoadBalanceTooltip(targetService) : '请先选择服务'}
                                placement="top"
                              >
                                <Button
                                  type="text"
                                  onClick={() => handleOpenLoadBalanceModal(targetService, false)}
                                  style={{
                                    color: targetService.serviceName ? '#2468F2' : '#B8BABF',
                                    padding: 0
                                  }}
                                  disabled={!targetService.serviceName}
                                >
                                  负载均衡算法
                                </Button>
                              </Tooltip>
                            )
                          }
                        ]}
                        dataSource={[targetService]}
                        pagination={false}
                        rowKey="key"
                      />
                    )}
                  </Form.Item>
                </div>
              </div>

              {/* 认证授权模块 */}
              <div className={`${styles["module-container"]} ${styles["auth-module"]}`}>
                <div className={styles["module-title"]}>认证授权</div>
                <div className={styles["module-content"]}>
                  <Form.Item
                    label="消费者认证">
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', paddingTop: 8 }}>
                      <Checkbox
                        checked={authEnabled}
                        disabled={srcProduct === 'pom'}
                        onChange={(e) => handleAuthEnabledChange(e.target.checked)}
                      >
                        开启认证
                      </Checkbox>
                      <div style={{ marginTop: 4, color: '#84878c', fontSize: 12 }}>
                        开启后，需配置可访问消费者，仅被授权的消费者可访问当前路由，也可在
                        <a
                          style={{ color: '#2468f2', cursor: 'pointer' }}
                          onClick={() => navigate(`/instance/base/info?instanceId=${instanceId}&activeMenu=consumer-list`)}
                        >
                          消费者管理
                        </a>
                        中进行授权
                      </div>

                      {authEnabled && (
                        <div className={styles.authTransferContainer}>
                          <Transfer
                            dataSource={availableConsumers.map(item => ({
                              key: item.consumerName,
                              title: item.consumerName,
                              description: item.description || '',
                              // 添加searchText字段用于搜索
                              searchText: item.consumerName,
                              // 如果消费者的srcProduct是pom，则禁用
                              disabled: item.srcProduct === 'pom'
                            }))}
                            targetKeys={selectedConsumerNames}
                            onChange={handleConsumerChange}
                            leftStyle={{
                              width: '240px',
                              height: '286px'
                            }}
                            rightStyle={{
                              width: '240px',
                              height: '286px'
                            }}
                            render={item => (
                              <Tooltip title={item.title}>
                                <div className={styles.transferItem} style={{
                                  opacity: item.disabled ? 0.5 : 1,
                                  color: item.disabled ? '#ccc' : undefined
                                }}>
                                  <div className={styles.transferItemTitle}>
                                    {item.title}
                                  </div>
                                  {!originalAuthorizedConsumers.includes(item.key as string) && (
                                    <Tag className={styles.unAuthTag}>
                                      未授权
                                    </Tag>
                                  )}
                                  {item.disabled && (
                                    <Tag className={styles.unAuthTag}>
                                      百舸推理
                                    </Tag>
                                  )}
                                </div>
                              </Tooltip>
                            )}
                            showSearch
                            filterOption={(inputValue, item) => {
                              return item.title?.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1;
                            }}
                            locale={{
                              searchPlaceholder: '请输入消费者名称',
                              sourceNoData: (
                                <Empty
                                  imageStyle={{ height: 100 }}
                                  description="暂未创建消费者"
                                />
                              )
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </Form.Item>
                </div>
              </div>

              {/* Token限流模块 */}
              <div className={`${styles["module-container"]}`}>
                <div className={styles["module-title"]}>高级策略</div>
                <div className={styles["module-content"]}>
                  <Form.Item label="Token 限流" style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', paddingTop: 8 }}>
                      <Checkbox
                        checked={tokenRateLimitEnabled}
                        onChange={(e) => handleTokenRateLimitEnabledChange(e.target.checked)}
                      >
                        开启 Token 限流
                      </Checkbox>
                      <div style={{ marginTop: 4, color: '#84878c', fontSize: 12 }}>
                        开启后，可配置基于消费者、请求头或请求参数的 Token 限流策略，限制在指定时间窗口内的 Token 使用量
                      </div>

                      {tokenRateLimitEnabled && (
                        <div style={{ width: '100%', marginTop: 16 }}>
                          <Table
                            style={{ width: '100%' }}
                            columns={[
                              {
                                title: '限流类型',
                                dataIndex: 'matchCondition',
                                key: 'matchCondition.type',
                                width: '120px',
                                render: (matchCondition, record) => (
                                  <Select
                                    value={matchCondition.type}
                                    onChange={(value) => handleUpdateRateLimitStrategy(record.key, 'matchCondition.type', value)}
                                    style={{ width: '100%' }}
                                  >
                                    <Option value="consumer">按消费者</Option>
                                    <Option value="header">按请求头</Option>
                                    <Option value="query_param">按请求参数</Option>
                                  </Select>
                                )
                              },
                              {
                                title: '限流条件',
                                key: 'condition',
                                width: '300px',
                                render: (_, record) => {
                                  const { matchCondition } = record;

                                  if (matchCondition.type === 'consumer') {
                                    return (
                                      <Select
                                        value={matchCondition.value || undefined}
                                        onChange={(value) => handleUpdateRateLimitStrategy(record.key, 'matchCondition.value', value)}
                                        style={{ width: '100%' }}
                                        placeholder="请选择消费者"
                                        onFocus={() => {
                                          if (availableConsumers.length === 0) {
                                            fetchConsumers();
                                          }
                                        }}
                                      >
                                        {availableConsumers.map(consumer => (
                                          <Option key={consumer.consumerId} value={consumer.consumerName}>
                                            {consumer.consumerName}
                                          </Option>
                                        ))}
                                      </Select>
                                    );
                                  } else {
                                    const isHeader = matchCondition.type === 'header';
                                    return (
                                      <div style={{ display: 'flex', gap: 8 }}>
                                        <Input
                                          value={matchCondition.key || ''}
                                          onChange={(e) => handleUpdateRateLimitStrategy(record.key, 'matchCondition.key', e.target.value)}
                                          placeholder={isHeader ? '请输入Header名称' : '请输入Query参数名'}
                                          style={{ flex: 1 }}
                                        />
                                        <Input
                                          value={matchCondition.value || ''}
                                          onChange={(e) => handleUpdateRateLimitStrategy(record.key, 'matchCondition.value', e.target.value)}
                                          placeholder={isHeader ? '请输入Header值' : '请输入Query参数值'}
                                          style={{ flex: 1 }}
                                        />
                                      </div>
                                    );
                                  }
                                }
                              },
                              {
                                title: '时间单位',
                                dataIndex: 'limitConfig',
                                key: 'limitConfig.timeUnit',
                                width: '120px',
                                render: (limitConfig, record) => (
                                  <Select
                                    value={limitConfig.timeUnit || undefined}
                                    onChange={(value) => handleUpdateRateLimitStrategy(record.key, 'limitConfig.timeUnit', value)}
                                    style={{ width: '100%' }}
                                    placeholder="请选择"
                                  >
                                    <Option value="second">每秒</Option>
                                    <Option value="minute">每分钟</Option>
                                    <Option value="hour">每小时</Option>
                                    <Option value="day">每天</Option>
                                  </Select>
                                )
                              },
                              {
                                title: 'Token数量',
                                dataIndex: 'limitConfig',
                                key: 'limitConfig.tokenAmount',
                                width: '150px',
                                render: (limitConfig, record) => (
                                  <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                                    <InputNumber
                                      value={limitConfig.tokenAmount || undefined}
                                      onChange={(value) => handleUpdateRateLimitStrategy(record.key, 'limitConfig.tokenAmount', value)}
                                      style={{ width: '100%', backgroundColor: '#ffffff', borderRadius: '4px' }}
                                      min={1}
                                      max={1000000}
                                      placeholder="请输入Token数量"
                                    />
                                  </div>
                                )
                              },
                              {
                                title: '操作',
                                key: 'action',
                                width: '80px',
                                render: (_, record) => (
                                  <Button
                                    type="text"
                                    onClick={() => handleRemoveRateLimitStrategy(record.key)}
                                    className={styles.deleteButton}
                                    disabled={rateLimitStrategies.length <= 1}
                                  >
                                    删除
                                  </Button>
                                )
                              }
                            ]}
                            dataSource={rateLimitStrategies}
                            pagination={false}
                            rowKey="key"
                          />

                          <Button
                            type="text"
                            icon={<OutlinedPlus />}
                            onClick={handleAddRateLimitStrategy}
                            style={{ marginTop: 16, color: '#2468F2', paddingLeft: 0 }}
                          >
                            添加限流策略
                          </Button>
                        </div>
                      )}
                    </div>
                  </Form.Item>

                  <Form.Item label="超时策略" tooltip="开启后，可配置超时时间，超过该时间后，请求将被终止" style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', padding: '6px 0' }}>
                      <Checkbox
                        checked={timeoutPolicyEnabled}
                        onChange={(e) => setTimeoutPolicyEnabled(e.target.checked)}
                      >
                        开启超时
                      </Checkbox>
                    </div>
                  </Form.Item>
                  {timeoutPolicyEnabled && (
                    <div style={{ width: '100%', marginBottom: 16 }}>
                      <Form.Item
                        label="超时时间"
                        required
                        style={{ marginBottom: 0 }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <InputNumber
                            value={timeoutValue}
                            onChange={(value) => setTimeoutValue(value)}
                            onBlur={() => {
                              if (!timeoutValue || timeoutValue < 1) {
                                setTimeoutValue(60);
                              }
                            }}
                            style={{ width: 80 }}
                            min={1}
                            max={3600}
                            placeholder="60"
                          />
                          <span style={{ marginLeft: 8 }}>秒</span>
                        </div>
                      </Form.Item>
                    </div>
                  )}

                  <Form.Item label="重试策略" tooltip="开启后，可配置重试条件和重试次数，当请求失败时，将根据配置进行重试" style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', padding: '6px 0' }}>
                      <Checkbox
                        checked={retryPolicyEnabled}
                        onChange={(e) => setRetryPolicyEnabled(e.target.checked)}
                      >
                        开启重试
                      </Checkbox>
                    </div>
                  </Form.Item>
                  {retryPolicyEnabled && (
                    <div style={{ width: '100%', marginTop: 16 }}>
                      <Form.Item
                        label="重试条件"
                        required
                        style={{ marginBottom: 16 }}
                      >
                        <Select
                          mode="multiple"
                          value={retryConditions}
                          onChange={(value) => setRetryConditions(value)}
                          style={{ width: '540px' }}
                          placeholder="请选择重试条件"
                          showSelectAll={true}
                          optionLabelProp="label"
                        >
                          {retryConditionOptions.map(option => (
                            <Option key={option.value} value={option.value} label={option.label}>
                              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                <span style={{ color: '#262626' }}>{option.label}</span>
                                <span style={{ color: '#8C8C8C', fontSize: '12px', marginLeft: '8px', flex: 1, textAlign: 'left' }}>
                                  {option.description}
                                </span>
                              </div>
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>

                      <Form.Item
                        label="重试次数"
                        required
                        style={{ marginBottom: 0 }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <InputNumber
                            value={retryAttempts}
                            onChange={(value) => setRetryAttempts(value)}
                            onBlur={() => {
                              if (!retryAttempts || retryAttempts < 1) {
                                setRetryAttempts(3);
                              }
                            }}
                            style={{ width: 80 }}
                            min={1}
                            max={100}
                            placeholder="3"
                          />
                          <span style={{ marginLeft: 8 }}>次</span>
                        </div>
                      </Form.Item>
                    </div>
                  )}
                </div>
              </div>
            </Form>
          )}
        </div>
      </div>

      <div className={styles['create-instance-footer']}>
        <div className={styles['create-instance-footer-content']}>
          <Button type="primary" onClick={handleSubmit} loading={submitting}>
            发布
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </div>
      </div>

      {/* 负载均衡算法弹窗 */}
      <Modal
        title="负载均衡配置"
        visible={loadBalanceModalVisible}
        onOk={handleLoadBalanceModalOk}
        onCancel={handleLoadBalanceModalCancel}
        okText="确定"
        cancelText="取消"
        width={500}
      >
        <Form
          labelAlign="left"
          labelWidth={85}
        >
          <Form.Item label="服务名称">
            <span>
              {currentEditService?.serviceName || ''}
            </span>
          </Form.Item>
          <Form.Item
            label="负载均衡算法"
            required
            validateStatus={modalFormErrors.loadBalanceAlgorithm ? 'error' : ''}
            help={modalFormErrors.loadBalanceAlgorithm}
          >
            <Select
              value={currentEditService?.loadBalanceAlgorithm}
              onChange={handleModalLoadBalanceChange}
              style={{ width: '100%' }}
              placeholder="请选择负载均衡算法"
            >
              {loadBalanceOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 当选择哈希一致性时显示Hash方式 */}
          {currentEditService?.loadBalanceAlgorithm === 'consistent-hash' && (
            <Form.Item
              label="Hash 方式"
              required
              validateStatus={modalFormErrors.hashType ? 'error' : undefined}
              help={modalFormErrors.hashType}
            >
              <Select
                value={currentEditService?.hashType}
                onChange={handleHashTypeChange}
                style={{ width: '100%' }}
                placeholder="请选择 Hash 方式"
              >
                {hashTypeOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}

          {/* 根据Hash方式显示对应的输入框 */}
          {currentEditService?.loadBalanceAlgorithm === 'consistent-hash' && currentEditService?.hashType === 'header' && (
            <Form.Item
              label="请求头"
              required
              validateStatus={modalFormErrors.hashKey ? 'error' : undefined}
              help={modalFormErrors.hashKey}
            >
              <Input
                value={currentEditService?.hashKey}
                onChange={(e) => handleHashKeyChange(e.target.value)}
                placeholder="请输入请求头"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}

          {currentEditService?.loadBalanceAlgorithm === 'consistent-hash' && currentEditService?.hashType === 'query_param' && (
            <Form.Item
              label="请求参数"
              required
              validateStatus={modalFormErrors.hashKey ? 'error' : undefined}
              help={modalFormErrors.hashKey}
            >
              <Input
                value={currentEditService?.hashKey}
                onChange={(e) => handleHashKeyChange(e.target.value)}
                placeholder="请输入请求参数"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}

          {currentEditService?.loadBalanceAlgorithm === 'consistent-hash' && currentEditService?.hashType === 'cookie' && (
            <Form.Item
              label="Cookie名称"
              required
              validateStatus={modalFormErrors.hashKey ? 'error' : undefined}
              help={modalFormErrors.hashKey}
            >
              <Input
                value={currentEditService?.hashKey}
                onChange={(e) => handleHashKeyChange(e.target.value)}
                placeholder="请输入Cookie名称"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default RouteEdit; 