import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getQueryParams, useRegion } from '@baidu/bce-react-toolkit';
import { Button, Form, Input, Alert, Select, Checkbox, Table, Switch, Transfer, Modal, Space, toast, Empty, Loading, Tooltip, Radio, InputNumber } from 'acud';
import { OutlinedLeft, OutlinedDelete, OutlinedPlus, OutlinedQuestionCircle ,OutlinedRefresh} from 'acud-icon';
import { useRequest } from 'ahooks';

import urls from '@/utils/urls';
import styles from './index.module.less';
import { getServicesBySource, createRoute, getRouteList } from '@/apis/route';
import { getConsumerList } from '@/apis/consumer';
import { getServicePorts } from '@/apis/service';
import { getRegistrationInstance } from '@/apis/instance';
import { color } from 'echarts';

const { Option } = Select;

const RouteCreate: React.FC = () => {
  const { instanceId } = getQueryParams();
  const { region } = useRegion();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  // 实例详情状态
  const [instanceDetail, setInstanceDetail] = useState<any>({});

  // 基础配置状态
  const [routeName, setRouteName] = useState('');
  
  // 路由规则状态
  const [caseSensitive, setCaseSensitive] = useState(true);
  const [headerParams, setHeaderParams] = useState<Array<{key: string; matchType: string; value: string;}>>([]);
  const [queryParams, setQueryParams] = useState<Array<{key: string; matchType: string; value: string;}>>([]);
  
  // 路径重写状态
  const [rewriteEnabled, setRewriteEnabled] = useState(false);
  const [rewriteMode, setRewriteMode] = useState('standard'); // 目前只支持标准模式
  const [rewritePath, setRewritePath] = useState('');
  
  // 目标服务状态
  const [multiService, setMultiService] = useState(false);
  const [trafficDistributionStrategy, setTrafficDistributionStrategy] = useState('ratio');
  const [targetService, setTargetService] = useState({
    key: '0',
    serviceSource: 'CCE',
    serviceName: '',
    namespace: '',
    servicePort: null as number | null,
    loadBalanceAlgorithm: 'round-robin',
    hashType: undefined as string | undefined,
    hashKey: undefined as string | undefined
  });
  const [targetServices, setTargetServices] = useState<Array<{
    key: string;
    serviceSource: string;
    serviceName: string;
    namespace: string;
    servicePort: number | null;
    loadBalanceAlgorithm: string;
    hashType?: string;
    hashKey?: string;
    requestRatio?: number;
    modelName?: string;
  }>>([{
    key: '0',
    serviceSource: 'CCE',
    serviceName: '',
    namespace: '',
    servicePort: null,
    loadBalanceAlgorithm: 'round-robin',
    requestRatio: 100
  }]);
  
  // 认证授权状态
  const [authEnabled, setAuthEnabled] = useState(false);
  const [availableConsumers, setAvailableConsumers] = useState<any[]>([]);
  const [selectedConsumers, setSelectedConsumers] = useState<string[]>([]);
  
  // 高级策略状态
  const [tokenRateLimitEnabled, setTokenRateLimitEnabled] = useState(false);
  const [rateLimitStrategies, setRateLimitStrategies] = useState<Array<{
    key: string;
    matchCondition: {
      type: 'consumer' | 'header' | 'query_param';
      key: string;
      value: string;
    };
    limitConfig: {
      timeUnit: 'second' | 'minute' | 'hour' | 'day' | '';
      tokenAmount: number | null;
    };
  }>>([{
    key: '0',
    matchCondition: {
      type: 'consumer',
      key: '',
      value: ''
    },
    limitConfig: {
      timeUnit: '',
      tokenAmount: null
    }
  }]);
  
  // 超时策略状态
  const [timeoutPolicyEnabled, setTimeoutPolicyEnabled] = useState(false);
  const [timeoutValue, setTimeoutValue] = useState<number>(60);

  // 重试策略状态
  const [retryPolicyEnabled, setRetryPolicyEnabled] = useState(false);
  const [retryConditions, setRetryConditions] = useState<string[]>(['connect-failure', 'refused-stream', 'retriable-status-codes', 'cancelled', 'unavailable']);
  const [retryAttempts, setRetryAttempts] = useState<number>(3);
  
  // 选项数据
  const [serviceOptions, setServiceOptions] = useState<any[]>([]);
  
  // 匹配类型选项
  const matchTypeOptions = [
    { label: '前缀匹配', value: 'prefix' },
    { label: '精确匹配', value: 'exact' },
  ];
  
  // HTTP方法选项
  const httpMethodOptions = [
    { label: 'GET', value: 'GET' },
    { label: 'POST', value: 'POST' },
    { label: 'PUT', value: 'PUT' },
    { label: 'DELETE', value: 'DELETE' },
    { label: 'OPTIONS', value: 'OPTIONS' },
    { label: 'HEAD', value: 'HEAD' },
    { label: 'PATCH', value: 'PATCH' }
  ];
  
  // 负载均衡算法选项
  const loadBalanceOptions = [
    { label: '轮询', value: 'round-robin' },
    { label: '随机', value: 'random' },
    { label: '最小连接数', value: 'least-conn' },
    { label: '哈希一致性', value: 'consistent-hash' },
  ];

  // Hash方式选项
  const hashTypeOptions = [
    { label: '基于Header', value: 'header' },
    { label: '基于请求参数', value: 'query_param' },
    { label: '基于源IP', value: 'ip' },
    { label: '基于Cookie', value: 'cookie' },
  ];

  // 添加服务端口列表状态
  const [servicePorts, setServicePorts] = useState<string[]>([]);
  const [servicePortsLoading, setServicePortsLoading] = useState(false);

  // 新增路由名称是否已存在状态
  const [routeNameExists, setRouteNameExists] = useState(false);
  const [nameValidating, setNameValidating] = useState(false);

  // 添加负载均衡算法弹窗状态
  const [loadBalanceModalVisible, setLoadBalanceModalVisible] = useState(false);
  const [currentEditService, setCurrentEditService] = useState<{
    key: string;
    serviceName: string;
    loadBalanceAlgorithm: string;
    isMultiService: boolean;
    hashType?: string;
    hashKey?: string;
  } | null>(null);
  
  // 添加负载均衡弹窗表单验证状态
  const [modalFormErrors, setModalFormErrors] = useState<{
    loadBalanceAlgorithm?: string;
    hashType?: string;
    hashKey?: string;
  }>({});

  // 重试条件选项
  const retryConditionOptions = [
    { label: '5xx', value: '5xx', description: '后端服务返回任何 5xx 响应，或发生连接断开、重置、读取超时事件' },
    { label: 'reset', value: 'reset', description: '发生连接断开、重置、读取超时事件' },
    { label: 'connect-failure', value: 'connect-failure', description: '请求连接断开' },
    { label: 'refused-stream', value: 'refused-stream', description: '后端服务以 REFUSED_STREAM 错误代码来重置连接' },
    { label: 'retriable-status-codes', value: 'retriable-status-codes', description: '后端服务响应错误的 HTTP 状态的任何上游请求的重试状态码' },
    { label: 'cancelled', value: 'cancelled', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 cancelled' },
    { label: 'deadline-exceeded', value: 'deadline-exceeded', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 deadline-exceeded' },
    { label: 'internal', value: 'internal', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 internal' },
    { label: 'resource-exhausted', value: 'resource-exhausted', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 resource-exhausted' },
    { label: 'unavailable', value: 'unavailable', description: '后端 gRPC 服务响应头中的 gRPC 状态码为 unavailable' },
  ];

  // 获取实例详情
  const fetchInstanceDetail = async () => {
    try {
      // console.log('请求实例详情，instanceId:', instanceId, '地域:', region);
      const res = await getRegistrationInstance(instanceId, region);
      // console.log('获取实例详情成功:', res);
      if (res?.success && res?.result) {
        setInstanceDetail(res.result);
      }
    } catch (error) {
      console.error('获取实例详情失败:', error);
    }
  };

  // 获取服务列表
  const { loading: serviceLoading, run: fetchServices } = useRequest(
    (serviceSource: string) => {
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      console.log('获取服务列表地域信息:', instanceRegion);
      return getServicesBySource(instanceId as string, serviceSource, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        // 处理不同的响应结构情况
        let serviceData: any[] = [];
        
        // 如果是直接返回数组
        if (Array.isArray(res)) {
          serviceData = res;
        } 
        // 如果是返回带有result字段的对象结构
        else if (res && res.result && Array.isArray(res.result)) {
          serviceData = res.result;
        }
        
        if (serviceData.length > 0) {
          const options = serviceData.map(item => ({
            label: item.serviceName,
            value: item.serviceName,
            namespace: item.namespace || '',
            clusterId: item.clusterId || ''
          }));
          setServiceOptions(options);
        } else {
          setServiceOptions([]);
        }
      },
      onError: (error) => {
        console.error('获取服务列表失败:', error);
        toast.error({
          message: '获取服务列表失败',
          duration: 5,
        });
        setServiceOptions([]);
      }
    }
  );

  // 获取消费者列表
  const { loading: consumerLoading, run: fetchConsumers } = useRequest(
    () => {
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      return getConsumerList(instanceId as string, {
        pageNo: 1,
        pageSize: 100,
        orderBy: 'createTime',
        order: 'desc'
      }, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.page?.result) {
          setAvailableConsumers(res.page.result);
        }
      },
      onError: (error) => {
        console.error('获取消费者列表失败:', error);
        toast.error({
          message: '获取消费者列表失败',
          duration: 5,
        });
      }
    }
  );

  // 添加获取服务端口的函数
  const fetchServicePorts = async (clusterId: string, serviceName: string, namespace: string) => {
    try {
      setServicePortsLoading(true);
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      const res = await getServicePorts(clusterId, serviceName, namespace, instanceRegion);
      
      if (res?.success && res?.result) {
        // 处理端口数据并去重
        const portsData = res.result || [];
        // 提取端口号并去重
        const uniquePorts = Array.from(new Set(
          portsData.map(portInfo => {
            // 分割字符串，只取端口号部分
            return portInfo.split(' ')[0];
          })
        ));
        setServicePorts(uniquePorts);
      } else {
        setServicePorts([]);
      }
    } catch (error) {
      console.error('获取服务端口信息失败:', error);
      toast.error({
        message: '获取服务端口信息失败',
        duration: 5,
      });
      setServicePorts([]);
    } finally {
      setServicePortsLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (instanceId) {
      // 先获取实例详情
      fetchInstanceDetail();
      // 设置请求方法字段的初始值为空数组
      form.setFieldsValue({ methods: [] });
    }
  }, [instanceId]);

  // 当实例详情加载完成后，获取服务列表
  useEffect(() => {
    if (instanceDetail?.region) {
      fetchServices('CCE');
    }
  }, [instanceDetail?.region]);

  // 处理提交
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      
      // 验证路径重写配置
      if (rewriteEnabled && !rewritePath.trim()) {
        toast.error({
          message: '开启路径重写时，重写路径为必填项',
          duration: 5,
        });
        setSubmitting(false);
        return;
      }
      
      // 验证目标服务配置
      if (!multiService) {
        // 单服务验证
        if (!targetService.serviceName) {
          toast.error({
              message: '请选择服务名称',
              duration: 5,
          });
          setSubmitting(false);
          return;
        }
        
        if (!targetService.servicePort) {
          toast.error({
            message: '请选择服务端口',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
      } else {
        // 多服务验证
        if (targetServices.length === 0) {
          toast.error({
            message: '请至少添加一个服务',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
        
        // 验证每个服务的必填字段
        for (const service of targetServices) {
          if (!service.serviceName) {
            toast.error({
              message: '请为所有服务选择服务名称',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
          
          if (!service.servicePort) {
            toast.error({
              message: '请为所有服务选择服务端口',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
        }
        
        // 验证比例分发时的比例总和
        if (trafficDistributionStrategy === 'ratio' && !isRatioValid()) {
          toast.error({
            message: `请求比例总和必须为100%，当前为${calculateTotalRatio()}%`,
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
        
        // 验证模型名称分发时的模型名称唯一性
        if (trafficDistributionStrategy === 'model_name') {
          const modelNames = targetServices.map(s => s.modelName).filter(Boolean);
          const uniqueModelNames = new Set(modelNames);
          if (modelNames.length !== uniqueModelNames.size) {
            toast.error({
              message: '模型名称不能重复',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
          
          // 检查是否所有服务都配置了模型名称
          const hasEmptyModelName = targetServices.some(s => !s.modelName);
          if (hasEmptyModelName) {
            toast.error({
              message: '请为所有服务配置模型名称',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
        }
      }
      
      // 验证Token限流配置
      if (tokenRateLimitEnabled) {
        for (const strategy of rateLimitStrategies) {
          if (!strategy.matchCondition.type) {
            toast.error({
              message: '请选择限流类型',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
          
          if (strategy.matchCondition.type === 'consumer' && !strategy.matchCondition.value) {
            toast.error({
              message: '请选择限流消费者',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
          
          if (strategy.matchCondition.type !== 'consumer' && (!strategy.matchCondition.key || !strategy.matchCondition.value)) {
            toast.error({
              message: '请完整配置限流条件',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
          
          if (!strategy.limitConfig.timeUnit) {
            toast.error({
              message: '请选择时间单位',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
          
          if (!strategy.limitConfig.tokenAmount || strategy.limitConfig.tokenAmount <= 0) {
            toast.error({
              message: 'Token数量必须大于0',
              duration: 5,
            });
            setSubmitting(false);
            return;
          }
        }
      }

      // 验证超时策略配置
      if (timeoutPolicyEnabled) {
        if (!timeoutValue || timeoutValue <= 0) {
          toast.error({
            message: '超时时间必须大于0',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
      }

      // 验证重试策略配置
      if (retryPolicyEnabled) {
        if (!retryConditions || retryConditions.length === 0) {
          toast.error({
            message: '请至少选择一个重试条件',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
        
        if (!retryAttempts || retryAttempts <= 0) {
          toast.error({
            message: '重试次数必须大于0',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
      }

      try {
        const values = await form.validateFields();
        
        // 过滤掉空的请求头和请求参数
        const filteredHeaders = headerParams
          .filter(item => item.key && item.value)
          .map(item => ({
            key: item.key.trim(),
            matchType: item.matchType || 'exact',
            value: item.value.trim()
          }));
        
        const filteredQueryParams = queryParams
          .filter(item => item.key && item.value)
          .map(item => ({
            key: item.key.trim(),
            matchType: item.matchType || 'exact',
            value: item.value.trim()
          }));
        
        // 获取消费者名称列表
        const getConsumerNames = (consumerIds: string[]) => {
          return consumerIds.map(id => {
            const consumer = availableConsumers.find(c => c.consumerId === id);
            return consumer?.consumerName || '';
          }).filter(name => name !== '');
        };
        
        // 构建请求数据
        const requestData: any = {
          routeName: values.routeName,
          matchRules: {
            pathRule: {
              matchType: values.pathMatchType,
              value: values.pathValue,
              caseSensitive: caseSensitive
            },
            methods: Array.isArray(values.methods) ? values.methods : [],
            headers: filteredHeaders,
            queryParams: filteredQueryParams
          },
          multiService,
          authEnabled: authEnabled,
          allowedConsumers: authEnabled ? getConsumerNames(selectedConsumers) : []
        };
        
        // 添加路径重写配置
        requestData.rewrite = {
          enabled: rewriteEnabled
        };
        
        if (rewriteEnabled && rewritePath.trim()) {
          requestData.rewrite.path = rewritePath.trim();
        }
        
        
        // 根据服务模式构建目标服务配置
        if (!multiService) {
          // 单服务模式
          const serviceConfig: any = {
            serviceSource: targetService.serviceSource,
            serviceName: targetService.serviceName,
            namespace: targetService.namespace,
            servicePort: Number(targetService.servicePort),
            loadBalanceAlgorithm: targetService.loadBalanceAlgorithm
          };
          
          // 如果是哈希一致性，添加哈希配置
          if (targetService.loadBalanceAlgorithm === 'consistent-hash') {
            serviceConfig.hashType = targetService.hashType;
            // 当hashType为'ip'时传空字符串，其他情况传具体值
            if (targetService.hashType === 'ip') {
              serviceConfig.hashKey = '';
            } else {
              serviceConfig.hashKey = targetService.hashKey || '';
            }
          }
          
          requestData.targetService = serviceConfig;
        } else {
          // 多服务模式
          requestData.trafficDistributionStrategy = trafficDistributionStrategy;
          requestData.targetService = targetServices.map(service => {
            const baseService: any = {
              serviceSource: service.serviceSource,
              serviceName: service.serviceName,
              namespace: service.namespace,
              servicePort: Number(service.servicePort),
              loadBalanceAlgorithm: service.loadBalanceAlgorithm
            };
            
            // 如果是哈希一致性，添加哈希配置
            if (service.loadBalanceAlgorithm === 'consistent-hash') {
              baseService.hashType = service.hashType;
              // 当hashType为'ip'时传空字符串，其他情况传具体值
              if (service.hashType === 'ip') {
                baseService.hashKey = '';
              } else {
                baseService.hashKey = service.hashKey || '';
              }
            }
            
            if (trafficDistributionStrategy === 'ratio') {
              return { ...baseService, requestRatio: service.requestRatio };
            } else {
              return { ...baseService, modelName: service.modelName };
            }
          });
        }
        
        // 添加Token限流配置
        requestData.tokenRateLimit = {
          enabled: tokenRateLimitEnabled
        };
        
        if (tokenRateLimitEnabled) {
          requestData.tokenRateLimit.rule_items = rateLimitStrategies.map(strategy => ({
            match_condition: {
              type: strategy.matchCondition.type,
              key: strategy.matchCondition.type === 'consumer' ? '' : strategy.matchCondition.key,
              value: strategy.matchCondition.value
            },
            limit_config: {
              time_unit: strategy.limitConfig.timeUnit,
              token_amount: strategy.limitConfig.tokenAmount
            }
          }));
        }

        // 添加超时策略配置
        requestData.timeoutPolicy = {
          enabled: timeoutPolicyEnabled
        };
        
        if (timeoutPolicyEnabled) {
          requestData.timeoutPolicy.timeout = timeoutValue;
        }

        // 添加重试策略配置
        requestData.retryPolicy = {
          enabled: retryPolicyEnabled
        };
        
        if (retryPolicyEnabled) {
          requestData.retryPolicy.retryConditions = retryConditions.join(',');
          requestData.retryPolicy.numRetries = retryAttempts;
        }
        
        // 获取集群ID
        let clusterId;
        if (!multiService) {
          const selectedService = serviceOptions.find(option => 
            option.value === targetService.serviceName && option.namespace === targetService.namespace
          );
          clusterId = selectedService?.clusterId;
        } else {
          // 多服务时使用第一个服务的集群ID（假设所有服务在同一集群）
          const firstService = targetServices[0];
          const selectedService = serviceOptions.find(option => 
            option.value === firstService.serviceName && option.namespace === firstService.namespace
          );
          clusterId = selectedService?.clusterId;
        }
        
        if (!clusterId) {
          toast.error({
            message: '无法获取服务所属集群ID，请重新选择服务',
            duration: 5,
          });
          setSubmitting(false);
          return;
        }
        
        // 路由名称验证
        const valid = await validateRouteName(values.routeName);
        if (!valid) {
          form.setFields([
            { name: 'routeName', errors: ['已存在同名路由'] }
          ]);
          setSubmitting(false);
          return;
        }
        
        // 调用创建路由接口
        
        // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
        const instanceRegion = instanceDetail?.region || region;
        const res = await createRoute(
          instanceId as string,
          clusterId,
          requestData,
          instanceRegion
        );
        
        if (res && res.success) {
          toast.success({
            message:'路由创建成功',
            duration: 5,
          });
          goBack();
        } else {
          // 显示错误信息
          let errorMsg = '创建路由失败，请重试';
          
          if (res?.message) {
            if (typeof res.message === 'object' && res.message.global) {
              errorMsg = res.message.global;
            } else if (typeof res.message === 'string') {
              errorMsg = res.message;
            }
          }
          
          if (res?.code) {
            errorMsg = `${errorMsg} (${res.code})`;
          }
          
          console.error('创建路由失败:', res);
          toast.error({
            message: errorMsg,
            duration: 5,
          });
        }
      } catch (error: any) {
        console.error('创建路由失败:', error);
        let errorMessage = '表单验证失败或创建过程中出现错误';
        
        if (error?.response?.data) {
          const errorData = error.response.data;
          
          if (errorData.message) {
            if (typeof errorData.message === 'object' && errorData.message.global) {
              errorMessage = errorData.message.global;
            } else if (typeof errorData.message === 'string') {
              errorMessage = errorData.message;
            }
          }
          
          if (errorData.code) {
            errorMessage = `${errorMessage} (${errorData.code})`;
          }
        } else if (error?.message && typeof error.message === 'string') {
          errorMessage = error.message;
        }
        
        toast.error({
          message: errorMessage,
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('创建路由失败:', error);
      let errorMessage = '表单验证失败或创建过程中出现错误';
      if (error?.message && typeof error.message === 'string') {
        errorMessage = error.message;
      }
      toast.error({
        message: errorMessage,
        duration: 5,
      });
    } finally {
      setSubmitting(false);
    }
  };

  // 返回列表
  const goBack = () => {
    // 返回实例详情页的路由配置标签
    navigate(`/instance/base/info?instanceId=${instanceId}&activeMenu=route-config`);
  };

  // 处理取消
  const handleCancel = () => {
    Modal.confirm({
      title: '确认取消',
      content: '取消创建将丢失已填写的数据，确认要取消吗？',
      onOk: goBack,
      okText: '确认',
      cancelText: '取消'
    });
  };

  // 添加请求头参数
  const handleAddHeaderParam = () => {
    const newHeader = { key: '', matchType: '', value: '' };
    setHeaderParams([...headerParams, newHeader]);
  };

  // 删除请求头参数
  const handleRemoveHeaderParam = (index: number) => {
    const newHeaders = [...headerParams];
    newHeaders.splice(index, 1);
    setHeaderParams(newHeaders);
  };

  // 更新请求头参数
  const handleUpdateHeaderParam = (index: number, field: string, value: string) => {
    const newHeaders = [...headerParams];
    newHeaders[index] = { ...newHeaders[index], [field]: value };
    setHeaderParams(newHeaders);
  };

  // 添加请求参数
  const handleAddQueryParam = () => {
    setQueryParams([...queryParams, { key: '', matchType: '', value: '' }]);
  };

  // 删除请求参数
  const handleRemoveQueryParam = (index: number) => {
    const newParams = [...queryParams];
    newParams.splice(index, 1);
    setQueryParams(newParams);
  };

  // 更新请求参数
  const handleUpdateQueryParam = (index: number, field: string, value: string) => {
    const newParams = [...queryParams];
    newParams[index] = { ...newParams[index], [field]: value };
    setQueryParams(newParams);
  };

  // 处理服务来源变化
  const handleServiceSourceChange = (value: string) => {
    setTargetService({
      ...targetService,
      serviceSource: value,
      serviceName: '',
      namespace: ''
    });
    fetchServices(value);
  };

  // 修改服务名称选择变更函数
  const handleServiceNameChange = (value: string) => {
    const selected = serviceOptions.find(option => option.value === value);
    if (selected) {
      setTargetService({
        ...targetService,
        serviceName: value,
        namespace: selected.namespace,
        servicePort: null // 重置服务端口
      });
      
      // 如果有选中服务，则获取服务端口
      if (selected.clusterId && value && selected.namespace) {
        fetchServicePorts(selected.clusterId, value, selected.namespace);
      } else {
        setServicePorts([]);
      }
    }
  };

  // 修改服务端口变更函数
  const handleServicePortChange = (value: string) => {
    setTargetService({
      ...targetService,
      servicePort: parseInt(value, 10) || null
    });
  };



  // 处理认证开关变化
  const handleAuthEnabledChange = (checked: boolean) => {
    setAuthEnabled(checked);
    if (checked && availableConsumers.length === 0) {
      fetchConsumers();
    }
  };

  // 处理选择消费者变化
  const handleConsumerChange = (targetKeys: string[]) => {
    setSelectedConsumers(targetKeys);
  };

  const validateRouteName = async (name: string) => {
    if (!name) return false;
    setNameValidating(true);
    try {
      // 先查第一页，获取总数
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      const firstPageRes = await getRouteList(instanceId, {
        routeName: name,
        pageNo: 1,
        pageSize: 100,
        orderBy: 'createTime',
        order: 'desc'
      }, instanceRegion);

      if (!firstPageRes?.success) {
        return true;
      }

      const totalCount = firstPageRes.page?.totalCount || 0;
      let allRoutes = firstPageRes.page?.result || [];

      // 如果总数大于一页，需要查询剩余页
      if (totalCount > 100) {
        const totalPages = Math.ceil(totalCount / 100);
        // 明确声明为 Promise<any>[]，避免推断为 never[]
        const remainingRequests: Promise<any>[] = [];

        // 从第2页开始，构建剩余页的请求
        for (let page = 2; page <= totalPages; page++) {
          remainingRequests.push(
            getRouteList(instanceId, {
              routeName: name,
              pageNo: page,
              pageSize: 100,
              orderBy: 'createTime',
              order: 'desc'
            }, instanceRegion)
          );
        }

        // 并发请求剩余页
        const remainingResults = await Promise.all(remainingRequests);
        
        // 合并所有页的结果
        remainingResults.forEach(res => {
          if (res?.success && res.page?.result) {
            allRoutes = allRoutes.concat(res.page.result);
          }
        });
      }

      // 在所有路由中查找是否存在同名路由（区分大小写）
      const exists = allRoutes.some(route => route.routeName === name);
      setRouteNameExists(exists);
      return !exists;

    } catch (error) {
      console.error('校验路由名称失败:', error);
      setRouteNameExists(false);
      return true;
    } finally {
      setNameValidating(false);
    }
  };

  // 添加多服务的处理函数
  const handleMultiServiceChange = (e: any) => {
    const isMulti = e.target.value;
    setMultiService(isMulti);
    
    if (isMulti) {
      // 切换到多服务时，将当前单服务配置作为第一个服务
      setTargetServices([{
        key: '0',
        serviceSource: targetService.serviceSource,
        serviceName: targetService.serviceName,
        namespace: targetService.namespace,
        servicePort: targetService.servicePort,
        loadBalanceAlgorithm: targetService.loadBalanceAlgorithm,
        hashType: targetService.hashType,
        hashKey: targetService.hashKey,
        requestRatio: 100
      }]);
    } else {
      // 切换到单服务时，使用第一个服务的配置
      if (targetServices.length > 0) {
        const firstService = targetServices[0];
        setTargetService({
          key: firstService.key,
          serviceSource: firstService.serviceSource,
          serviceName: firstService.serviceName,
          namespace: firstService.namespace,
          servicePort: firstService.servicePort,
          loadBalanceAlgorithm: firstService.loadBalanceAlgorithm,
          hashType: (firstService as any).hashType,
          hashKey: (firstService as any).hashKey
        });
      }
    }
  };

  // 处理流量分发策略变化
  const handleTrafficDistributionStrategyChange = (e: any) => {
    const strategy = e.target.value;
    setTrafficDistributionStrategy(strategy);
    
    // 更新服务列表，根据策略添加对应字段，同时保留哈希配置
    const updatedServices = targetServices.map(service => {
      const baseService = {
        key: service.key,
        serviceSource: service.serviceSource,
        serviceName: service.serviceName,
        namespace: service.namespace,
        servicePort: service.servicePort,
        loadBalanceAlgorithm: service.loadBalanceAlgorithm,
        hashType: service.hashType,
        hashKey: service.hashKey
      };
      
      if (strategy === 'ratio') {
        return { ...baseService, requestRatio: service.requestRatio || 50 };
      } else {
        return { ...baseService, modelName: service.modelName || '' };
      }
    });
    
    setTargetServices(updatedServices);
  };

  // 添加服务
  const handleAddService = () => {
    const newKey = Date.now().toString();
    const newService: any = {
      key: newKey,
      serviceSource: 'CCE',
      serviceName: '',
      namespace: '',
      servicePort: null,
      loadBalanceAlgorithm: 'round-robin',
          hashType: undefined,
    hashKey: undefined
    };
    
    if (trafficDistributionStrategy === 'ratio') {
      newService.requestRatio = 0;
    } else {
      newService.modelName = '';
    }
    
    setTargetServices([...targetServices, newService]);
  };

  // 删除服务
  const handleRemoveService = (key: string) => {
    if (targetServices.length <= 1) {
      toast.warning({
        message: '至少需要保留一个服务',
        duration: 5,
      });
      return;
    }
    
    const newServices = targetServices.filter(service => service.key !== key);
    setTargetServices(newServices);
  };

  // 更新多服务配置
  const handleUpdateService = (key: string, field: string, value: any) => {
    const newServices = targetServices.map(service => {
      if (service.key === key) {
        const updatedService = { ...service, [field]: value };
        
        // 如果是服务名称变化，需要重置相关字段并获取端口信息
        if (field === 'serviceName') {
          const selected = serviceOptions.find(option => option.value === value);
          if (selected) {
            updatedService.namespace = selected.namespace;
            updatedService.servicePort = null;
            // 获取服务端口
            if (selected.clusterId && value && selected.namespace) {
              fetchServicePorts(selected.clusterId, value, selected.namespace);
            }
          }
        }
        
        return updatedService;
      }
      return service;
    });
    setTargetServices(newServices);
  };

  // 计算比例总和
  const calculateTotalRatio = () => {
    return targetServices.reduce((sum, service) => sum + (service.requestRatio || 0), 0);
  };

  // 检查比例是否有效
  const isRatioValid = () => {
    if (trafficDistributionStrategy !== 'ratio') return true;
    const total = calculateTotalRatio();
    return total === 100;
  };

  // Token限流相关处理函数
  const handleTokenRateLimitChange = (checked: boolean) => {
    setTokenRateLimitEnabled(checked);
    if (checked && rateLimitStrategies.length === 0) {
      setRateLimitStrategies([{
        key: '0',
        matchCondition: {
          type: 'consumer',
          key: '',
          value: ''
        },
        limitConfig: {
          timeUnit: '',
          tokenAmount: null
        }
      }]);
    }
  };

  // 添加限流策略
  const handleAddRateLimitStrategy = () => {
    const newKey = Date.now().toString();
    const newStrategy = {
      key: newKey,
      matchCondition: {
        type: 'consumer' as const,
        key: '',
        value: ''
      },
      limitConfig: {
        timeUnit: '' as const,
        tokenAmount: null
      }
    };
    setRateLimitStrategies([...rateLimitStrategies, newStrategy]);
  };

  // 删除限流策略
  const handleRemoveRateLimitStrategy = (key: string) => {
    if (rateLimitStrategies.length <= 1) {
      toast.warning({
        message: '至少需要保留一个限流策略',
        duration: 5,
      });
      return;
    }
    
    const newStrategies = rateLimitStrategies.filter(strategy => strategy.key !== key);
    setRateLimitStrategies(newStrategies);
  };

  // 更新限流策略
  const handleUpdateRateLimitStrategy = (key: string, field: string, value: any) => {
    const newStrategies = rateLimitStrategies.map(strategy => {
      if (strategy.key === key) {
        const [mainField, subField] = field.split('.');
        if (subField) {
          const currentValue = strategy[mainField as keyof typeof strategy];
          if (currentValue && typeof currentValue === 'object') {
            return {
              ...strategy,
              [mainField]: {
                ...currentValue,
                [subField]: value
              }
            };
          }
        } else {
          return { ...strategy, [field]: value };
        }
      }
      return strategy;
    });
    setRateLimitStrategies(newStrategies);
  };

  // 处理路径重写开关变化
  const handleRewriteEnabledChange = (checked: boolean) => {
    setRewriteEnabled(checked);
    if (!checked) {
      // 关闭时重置相关状态
      setRewritePath('');
      setRewriteMode('standard');
    }
  };

  // 处理重写路径变化
  const handleRewritePathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setRewritePath(value);
  };

  // 添加负载均衡算法弹窗处理函数
  const handleOpenLoadBalanceModal = (service: any, isMultiService: boolean) => {
    setCurrentEditService({
      key: service.key,
      serviceName: service.serviceName,
      loadBalanceAlgorithm: service.loadBalanceAlgorithm,
      hashType: service.hashType,
      hashKey: service.hashKey,
      isMultiService
    });
    setModalFormErrors({}); // 清除错误状态
    setLoadBalanceModalVisible(true);
  };

  const handleLoadBalanceModalOk = () => {
    if (!currentEditService) return;
    
    // 验证表单
    const errors: typeof modalFormErrors = {};
    
    // 验证负载均衡算法
    if (!currentEditService.loadBalanceAlgorithm) {
      errors.loadBalanceAlgorithm = '请选择负载均衡算法';
    }
    
    // 如果选择哈希一致性，验证相关字段
    if (currentEditService.loadBalanceAlgorithm === 'consistent-hash') {
      if (!currentEditService.hashType) {
        errors.hashType = '请选择 Hash 方式';
      } else {
        // 验证需要输入key的Hash方式
        const needsKey = ['header', 'query_param', 'cookie'].includes(currentEditService.hashType);
        if (needsKey && (!currentEditService.hashKey || currentEditService.hashKey.trim() === '')) {
          errors.hashKey = '请输入对应的值';
        }
      }
    }
    
    // 如果有错误，显示错误并阻止关闭弹窗
    if (Object.keys(errors).length > 0) {
      setModalFormErrors(errors);
      return;
    }
    
    // 验证通过，清除错误状态
    setModalFormErrors({});
    
    // 更新服务配置
    if (currentEditService.isMultiService) {
      // 更新多服务
      const updatedService = targetServices.find(s => s.key === currentEditService.key);
      if (updatedService) {
        const newTargetServices = targetServices.map(service => 
          service.key === currentEditService.key 
            ? {
                ...service,
                loadBalanceAlgorithm: currentEditService.loadBalanceAlgorithm,
                hashType: currentEditService.hashType,
                hashKey: currentEditService.hashKey
              }
            : service
        );
        setTargetServices(newTargetServices);
      }
    } else {
      // 更新单服务
      setTargetService({
        ...targetService,
        loadBalanceAlgorithm: currentEditService.loadBalanceAlgorithm,
        hashType: currentEditService.hashType,
        hashKey: currentEditService.hashKey
      });
    }
    
    setLoadBalanceModalVisible(false);
    setCurrentEditService(null);
  };

  const handleLoadBalanceModalCancel = () => {
    setLoadBalanceModalVisible(false);
    setCurrentEditService(null);
    setModalFormErrors({});
  };

  const handleModalLoadBalanceChange = (value: string) => {
    if (currentEditService) {
      setCurrentEditService({
        ...currentEditService,
        loadBalanceAlgorithm: value,
        // 当不是哈希一致性时，清空哈希相关配置
        hashType: value === 'consistent-hash' ? currentEditService.hashType : undefined,
        hashKey: value === 'consistent-hash' ? currentEditService.hashKey : undefined,
      });
      
      // 清除负载均衡算法的错误状态
      if (modalFormErrors.loadBalanceAlgorithm) {
        setModalFormErrors({
          ...modalFormErrors,
          loadBalanceAlgorithm: undefined
        });
      }
    }
  };

  // 处理Hash方式变化
  const handleHashTypeChange = (value: string) => {
    if (currentEditService) {
      setCurrentEditService({
        ...currentEditService,
        hashType: value,
        // 切换Hash方式时清空key值
        hashKey: undefined,
      });
      
      // 清除Hash方式的错误状态
      if (modalFormErrors.hashType) {
        setModalFormErrors({
          ...modalFormErrors,
          hashType: undefined
        });
      }
    }
  };

  // 处理Hash Key变化
  const handleHashKeyChange = (value: string) => {
    if (currentEditService) {
      setCurrentEditService({
        ...currentEditService,
        hashKey: value
      });
      
      // 清除Hash Key的错误状态
      if (modalFormErrors.hashKey) {
        setModalFormErrors({
          ...modalFormErrors,
          hashKey: undefined
        });
      }
    }
  };

  // 格式化负载均衡算法显示文本
  const formatLoadBalanceTooltip = (service: any) => {
    const algorithmLabels = {
      'round-robin': '轮询',
      'random': '随机',
      'least-conn': '最小连接数',
      'consistent-hash': '哈希一致性'
    };
    
    const hashTypeLabels = {
      'header': '基于Header',
      'query_param': '基于请求参数',
      'ip': '基于源IP',
      'cookie': '基于Cookie'
    };
    
    const lines = [algorithmLabels[service.loadBalanceAlgorithm] || service.loadBalanceAlgorithm];
    
    if (service.loadBalanceAlgorithm === 'consistent-hash' && service.hashType) {
      lines.push(`Hash 方式：${hashTypeLabels[service.hashType] || service.hashType}`);
      
      // 如果有hashKey，显示具体的key值
      if (service.hashKey && ['header', 'query_param', 'cookie'].includes(service.hashType)) {
        const keyLabels = {
          'header': '请求头',
          'query_param': '请求参数',
          'cookie': 'Cookie名称'
        };
        lines.push(`${keyLabels[service.hashType]}：${service.hashKey}`);
      }
    }
    
    return (
      <div>
        {lines.map((line, index) => (
          <div key={index}>{line}</div>
        ))}
      </div>
    );
  };

  return (
    <div className={styles['create-instance-wrap']}>
      <div className={styles['create-instance-header']}>
        <Button
          type="text"
          onClick={goBack}
          icon={<OutlinedLeft />}
          style={{ marginRight: '8px' ,color:'#83868c',fontSize:'14px'}}
        >
          返回列表
        </Button>
        <span style={{fontSize:'16px',fontWeight:'500',color:'#151b26'}}>创建路由</span>
      </div>
      <div className={styles['create-instance-content-wrap']}>
        <div className={styles['create-instance-content']}>
          <Form
            form={form}
            autoComplete="off"
            labelAlign="left"
            labelWidth={85} // 设置label宽度,表单全局生效
            wrapperCol={{ style: { flex: '1', width: 'auto' } }}
          >
            {/* 基础配置模块 */}
            <div className={`${styles["module-container"]} ${styles["basic-info-module"]}`}>
              <div className={styles["module-title"]}>基本信息</div>
              <div className={styles["module-content"]}>
                <Form.Item
                  label="路由名称"
                  name="routeName"
                  extra={<>
                    <span style={{ color: '#FF9326' }}>创建后不支持修改。</span>
                    支持大小写字母、数字以及-_特殊字符，长度限制在2-64之间
                  </>}
                  rules={[
                    { required: true, message: '请输入路由名称' },
                    { pattern: /^[a-zA-Z0-9_-]{2,64}$/, message: '路由名称长度为2-64个字符，可包含字母、数字、下划线和连字符' },
                    {
                      validator: async (_, value) => {
                        if (routeNameExists) {
                          return Promise.reject('已存在同名路由');
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                  validateStatus={routeNameExists ? 'error' : undefined}
                  help={routeNameExists ? '已存在同名路由' : undefined}
                >
                  <Input 
                    placeholder="请输入路由名称" 
                    style={{ width: 400 }} 
                    onChange={(e) => {
                      // 输入框值变化时，重置校验状态
                      setRouteNameExists(false);
                      form.setFields([{
                        name: 'routeName',
                        errors: []
                      }]);
                    }}
                    onBlur={e => validateRouteName(e.target.value)}
                    suffix={nameValidating ?<OutlinedRefresh style={{ animation: 'spin 1s linear infinite' }} />: null}
                  />
                </Form.Item>
              </div>
            </div>

            {/* 路由规则模块 */}
            <div className={`${styles["module-container"]} ${styles["route-config-module"]}`}>
              <div className={styles["module-title"]}>路由规则</div>
              <div className={styles["module-content"]}>
                <Form.Item label="匹配规则" required style={{marginBottom:16}}>
                  <div style={{ 
                    borderRadius: '6px', 
                    padding: '16px', 
                    display: 'inline-block',
                    background: '#F7F7F9',
                    width: '100%'
                  }}>
                    <Form.Item
                      label="路径（Path）"
                      style={{ marginBottom: 16 }}
                      labelCol={{ span: 4 }}
                      required
                    >
                      <Form.Item
                        name="pathValue"
                        noStyle
                        rules={[{ required: true, message: '请输入路径匹配值' }]}
                      >
                        <Input
                          placeholder="请输入路径匹配值，如 /user"
                          style={{ width: '360px !important' }}
                          className={styles.pathInput}
                          addonBefore={
                            <Form.Item
                              name="pathMatchType"
                              noStyle
                              initialValue="prefix"

                              rules={[{ required: true, message: '请选择路径匹配规则' }]}
                            >
                              <Select 
                                className={styles.pathSelector}
                              >
                                {matchTypeOptions.map(option => (
                                  <Option key={option.value} value={option.value}>
                                    {option.label}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          }
                        />
                      </Form.Item>
                    </Form.Item>
                    
                    <Form.Item
                      label="请求方法（Method）"
                      name="methods"
                      initialValue={[]}
                      style={{ marginBottom: 16 }}
                      labelCol={{  span: 4  }}
                      rules={[{ required: true, message: '请选择请求方法' }]}
                    >
                      <Select
                        mode="multiple"
                        placeholder="请选择请求方法"
                        style={{ width:360 }}
                        options={httpMethodOptions}
                        getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
                        listHeight={200}
                        tagRender={({ label, closable, onClose }) => (
                          <div style={{ 
                            display: 'inline-flex', 
                            alignItems: 'center', 
                            padding: '2px 8px', 
                            marginRight: '4px',
                            backgroundColor: '#f5f5f5', 
                            borderRadius: '4px',
                            fontSize: '14px'
                          }}>
                            {label}
                            {closable && (
                              <span 
                                style={{ marginLeft: '4px', cursor: 'pointer' }}
                                onClick={onClose}
                              >
                                ×
                              </span>
                            )}
                          </div>
                        )}
                      />
                    </Form.Item>
                    
                    <Form.Item 
                      label="请求头（Header）" 
                      style={{ marginBottom: 16 }}
                      labelCol={{  span: 4  }}
                    >
                      <div style={{ width: '100%' }}>
                        {headerParams.map((header, index) => (
                          <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: 8, gap: 8, flexWrap: 'nowrap', whiteSpace: 'nowrap' }}>
                            <Input
                              placeholder="请输入 Header Key"
                              value={header.key}
                              style={{ width: '220px !important', flex: '0 0 220px' }}
                              onChange={(e) => handleUpdateHeaderParam(index, 'key', e.target.value)}
                             
                              onBlur={(e) => {
                                // 确保在失去焦点时更新headerParams
                                const newHeaders = [...headerParams];
                                newHeaders[index] = { ...newHeaders[index], key: e.target.value.trim() };
                                setHeaderParams(newHeaders);
                              }}
                            />
                            <Select
                              value={header.matchType ? header.matchType : undefined}
                              onChange={(value) => handleUpdateHeaderParam(index, 'matchType', value)}
                              style={{ width: '180px !important', flex: '0 0 180px' }}
                              placeholder="请选择匹配规则"
                              allowClear
                            >
                              {matchTypeOptions.map(option => (
                                <Option key={option.value} value={option.value}>
                                  {option.label}
                                </Option>
                              ))}
                            </Select>
                            <Input
                              placeholder="请输入 Header 值"
                              value={header.value}
                              style={{ width: '220px !important', flex: '0 0 220px' }}
                              onChange={(e) => handleUpdateHeaderParam(index, 'value', e.target.value)}
                              
                              onBlur={(e) => {
                                // 确保在失去焦点时更新headerParams
                                const newHeaders = [...headerParams];
                                newHeaders[index] = { ...newHeaders[index], value: e.target.value.trim() };
                                setHeaderParams(newHeaders);
                              }}
                            />
                            <span 
                              style={{ color: '#2468F2', marginLeft: '8px', cursor: 'pointer' }}
                              onClick={() => handleRemoveHeaderParam(index)}
                            >
                              删除
                            </span>
                          </div>
                        ))}
                        <Button 
                          type="text" 
                          icon={<OutlinedPlus />} 
                          onClick={handleAddHeaderParam}
                          style={{ paddingLeft: 0 ,color:'#2468F2'}}
                        >
                          添加请求头
                        </Button>
                      </div>
                    </Form.Item>
                    
                    <Form.Item 
                      label="请求参数（Query）" 
                      style={{ marginBottom: 0 }}
                      labelCol={{  span: 4  }}
                    >
                      <div style={{ width: '100%' }}>
                        {queryParams.map((param, index) => (
                          <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: 8, gap: 8, flexWrap: 'nowrap', whiteSpace: 'nowrap' }}>
                            <Input
                              placeholder="请输入 Query Key"
                              value={param.key}
                              style={{ width: '220px !important', flex: '0 0 220px' }}
                              onChange={(e) => handleUpdateQueryParam(index, 'key', e.target.value)}
                            
                            />
                            <Select
                              value={param.matchType ? param.matchType : undefined}
                              onChange={(value) => handleUpdateQueryParam(index, 'matchType', value)}
                              style={{ width: '180px !important', flex: '0 0 180px' }}
                              placeholder="请选择匹配规则"
                              allowClear
                            >
                              {matchTypeOptions.map(option => (
                                <Option key={option.value} value={option.value}>
                                  {option.label}
                                </Option>
                              ))}
                            </Select>
                            <Input
                              placeholder="请输入 Query 值"
                              value={param.value}
                              style={{ width: '220px !important', flex: '0 0 220px' }}
                              onChange={(e) => handleUpdateQueryParam(index, 'value', e.target.value)}
                            
                            />
                            <span 
                              style={{ color: '#2468F2', marginLeft: '8px', cursor: 'pointer' }}
                              onClick={() => handleRemoveQueryParam(index)}
                            >
                              删除
                            </span>
                          </div>
                        ))}
                        <Button 
                          type="text" 
                          icon={<OutlinedPlus />} 
                          onClick={handleAddQueryParam}
                          style={{ paddingLeft: 0 ,color:'#2468F2'}}
                        >
                          添加请求参数
                        </Button>
                      </div>
                    </Form.Item>
                  </div>
                </Form.Item>
                <Form.Item label="路径重写">
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', paddingTop:8}}>
                    <Checkbox
                      checked={rewriteEnabled}
                      onChange={(e) => handleRewriteEnabledChange(e.target.checked)}
                    >
                      开启路径重写
                    </Checkbox>
                    <div style={{ marginTop: 4, color: '#84878c', fontSize: 12 }}>
                      开启后，需配置重写路径，将覆盖重写原路径匹配值
                    </div>
                  </div>
                </Form.Item>
                
                {rewriteEnabled && (
                  <>
                    <Form.Item 
                      label="重写模式" 
                      required
                      style={{ margin: '16px 0 16px 0' }}
                      tooltip="标准模式：对于路径精确匹配的路由，进行完整覆盖重写；对于路径前缀匹配的路由，仅重写前缀匹配的部分"
                    >
                      <Radio.Group 
                        value={rewriteMode} 
                        onChange={(e: any) => setRewriteMode(e.target.value)}
                      >
                        <Radio.Button value="standard">标准</Radio.Button>
                      </Radio.Group>
                    </Form.Item>
                    
                    <Form.Item 
                      label="重写路径" 
                      required
                      style={{ marginBottom: 0 }}
                    >
                      <Input
                        value={rewritePath}
                        onChange={handleRewritePathChange}
                        placeholder="请输入重写路径，如 /default"
                        style={{ width: 360 }}
                      />
                    </Form.Item>
                  </>
                )}
              </div>
            </div>
            
            {/* 目标服务模块 */}
            <div className={`${styles["module-container"]}`}>
              <div className={styles["module-title"]}>目标服务</div>
              <div className={styles["module-content"]}>
                <Form.Item label="服务模式" required>
                  <div style={{ marginBottom: 16 }}>
                    <Radio.Group 
                      value={multiService}
                      onChange={handleMultiServiceChange}
                    >
                      <Radio.Button value={false}>单服务</Radio.Button>
                      <Radio.Button value={true}>多服务</Radio.Button>
                    </Radio.Group>
                  </div>
                </Form.Item>

                {multiService && (
                  <Form.Item label="流量策略" required>
                    <div style={{ marginBottom: 16 }}>
                      <Radio.Group
                        value={trafficDistributionStrategy}
                        onChange={handleTrafficDistributionStrategyChange}
                      >
                        <Radio.Button value="ratio">按比例</Radio.Button>
                        <Radio.Button value="model_name">按模型名称</Radio.Button>
                      </Radio.Group>
                    </div>
                  </Form.Item>
                )}

                <Form.Item 
                  label="目标服务" 
                  required
                  validateStatus={multiService && trafficDistributionStrategy === 'ratio' && !isRatioValid() ? 'error' : ''}
                  help={multiService && trafficDistributionStrategy === 'ratio' && !isRatioValid() ? 
                    `请求比例总和必须为100%，当前为${calculateTotalRatio()}%` : ''}
                >
                  {!multiService ? (
                    // 单服务表格
                    <Table
                      style={{ width: '100%' }}
                      columns={[
                        {
                          title: '服务来源',
                          dataIndex: 'serviceSource',
                          key: 'serviceSource',
                          render: () => (
                            <Select 
                              value={targetService.serviceSource} 
                              onChange={handleServiceSourceChange}
                              style={{ width: '100%' }}
                            >
                              <Option value="CCE">容器引擎 CCE</Option>
                            </Select>
                          )
                        },
                        {
                          title: '服务名称',
                          dataIndex: 'serviceName',
                          key: 'serviceName',
                          render: () => (
                            <Select
                              value={targetService.serviceName || undefined}
                              onChange={handleServiceNameChange}
                              style={{ width: '100%' }}
                              placeholder="请选择服务"
                              loading={serviceLoading}
                              disabled={!targetService.serviceSource}
                              showSearch
                              optionFilterProp="label"
                              filterOption={(input, option) => 
                                (option?.label?.toString().toLowerCase() || '').includes(input.toLowerCase())
                              }
                              dropdownMatchSelectWidth={false}
                              onFocus={() => {
                                if (targetService.serviceSource && serviceOptions.length === 0) {
                                  fetchServices(targetService.serviceSource);
                                }
                              }}
                              onDropdownVisibleChange={(open) => {
                                if (open && targetService.serviceSource && serviceOptions.length === 0) {
                                  fetchServices(targetService.serviceSource);
                                }
                              }}
                              dropdownRender={menu => (
                                <>
                                  {menu}
                                  {serviceOptions.length === 0 && !serviceLoading && (
                                    <div style={{ padding: '8px', textAlign: 'center', color: '#8c8c8c' }}>
                                      暂无服务数据
                                    </div>
                                  )}
                                </>
                              )}
                            >
                              {serviceOptions.map(option => (
                                <Option 
                                  key={option.value} 
                                  value={option.value}
                                  label={option.label}
                                >
                                  <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                                    <span style={{ fontWeight: 'normal' }}>{option.value}</span>
                                    <span style={{ color: '#8c8c8c', fontSize: '12px', marginLeft: '8px' }}>
                                      （{option.namespace}）
                                    </span>
                                  </div>
                                </Option>
                              ))}
                            </Select>
                          )
                        },
                        {
                          title: '服务端口',
                          dataIndex: 'servicePort',
                          key: 'servicePort',
                          render: () => (
                            <Select
                              value={targetService.servicePort ? targetService.servicePort.toString() : undefined}
                              onChange={handleServicePortChange}
                              placeholder="请选择服务端口"
                              style={{ width: '100%' }}
                              loading={servicePortsLoading}
                              disabled={!targetService.serviceName}
                              onFocus={() => {
                                if (targetService.serviceName) {
                                  const selected = serviceOptions.find(option => 
                                    option.value === targetService.serviceName && 
                                    option.namespace === targetService.namespace
                                  );
                                  if (selected?.clusterId) {
                                    fetchServicePorts(selected.clusterId, targetService.serviceName, targetService.namespace);
                                  }
                                }
                              }}
                              onDropdownVisibleChange={(open) => {
                                if (open && targetService.serviceName) {
                                  const selected = serviceOptions.find(option => 
                                    option.value === targetService.serviceName && 
                                    option.namespace === targetService.namespace
                                  );
                                  if (selected?.clusterId) {
                                    fetchServicePorts(selected.clusterId, targetService.serviceName, targetService.namespace);
                                  }
                                }
                              }}
                            >
                              {servicePorts.map(port => (
                                <Option key={port} value={port}>
                                  {port}
                                </Option>
                              ))}
                            </Select>
                          )
                        },
                        {
                          title: '操作',
                          key: 'action',
                          width: '100px',
                          render: () => (
                            <Tooltip 
                              title={targetService.serviceName ? formatLoadBalanceTooltip(targetService) : '请先选择服务'}
                              placement="top"
                            >
                              <Button
                                type="text"
                                onClick={() => handleOpenLoadBalanceModal(targetService, false)}
                                style={{ 
                                  color: targetService.serviceName ? '#2468F2' : '#B8BABF', 
                                  padding: 0 
                                }}
                                disabled={!targetService.serviceName}
                              >
                                负载均衡
                              </Button>
                            </Tooltip>
                          )
                        }
                      ]}
                      dataSource={[targetService]}
                      pagination={false}
                      rowKey="key"
                    />
                  ) : (
                    // 多服务表格
                    <div>
                      <Table
                        style={{ width: '100%' }}
                        columns={[
                          {
                            title: '服务来源',
                            dataIndex: 'serviceSource',
                            key: 'serviceSource',
                            render: (_, record) => (
                              <Select 
                                value={record.serviceSource} 
                                onChange={(value) => handleUpdateService(record.key, 'serviceSource', value)}
                                style={{ width: '100%' }}
                              >
                                <Option value="CCE">容器引擎 CCE</Option>
                              </Select>
                            )
                          },
                          {
                            title: '服务名称',
                            dataIndex: 'serviceName',
                            key: 'serviceName',
                            render: (_, record) => (
                              <Select
                                value={record.serviceName || undefined}
                                onChange={(value) => handleUpdateService(record.key, 'serviceName', value)}
                                style={{ width: '100%' }}
                                placeholder="请选择服务"
                                loading={serviceLoading}
                                disabled={!record.serviceSource}
                                showSearch
                                optionFilterProp="label"
                                filterOption={(input, option) => 
                                  (option?.label?.toString().toLowerCase() || '').includes(input.toLowerCase())
                                }
                              >
                                {serviceOptions.map(option => (
                                  <Option 
                                    key={option.value} 
                                    value={option.value}
                                    label={option.label}
                                  >
                                    <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                                      <span style={{ fontWeight: 'normal' }}>{option.value}</span>
                                      <span style={{ color: '#8c8c8c', fontSize: '12px', marginLeft: '8px' }}>
                                        （{option.namespace}）
                                      </span>
                                    </div>
                                  </Option>
                                ))}
                              </Select>
                            )
                          },
                          {
                            title: '服务端口',
                            dataIndex: 'servicePort',
                            key: 'servicePort',
                            render: (_, record) => (
                              <Select
                                value={record.servicePort ? record.servicePort.toString() : undefined}
                                onChange={(value) => handleUpdateService(record.key, 'servicePort', parseInt(value, 10))}
                                placeholder="请选择端口"
                                style={{ width: '100%' }}
                                disabled={!record.serviceName}
                              >
                                {servicePorts.map(port => (
                                  <Option key={port} value={port}>
                                    {port}
                                  </Option>
                                ))}
                              </Select>
                            )
                          },
                          ...(trafficDistributionStrategy === 'ratio' ? [{
                            title: '请求比例（%）',
                            dataIndex: 'requestRatio',
                            key: 'requestRatio',
                            render: (_, record) => (
                              <div style={{ display: 'flex', alignItems: 'center', height: '100%', backgroundColor: '#ffffff', borderRadius: '4px' }}>
                                <InputNumber
                                  value={record.requestRatio || 0}
                                  onChange={(value) => handleUpdateService(record.key, 'requestRatio', value || 0)}
                                  style={{ width: '100%' }}
                                  min={0}
                                  max={100}
                                  // symmetryMode
                                  placeholder="0"
                                />
                              </div>
                            )
                          }] : [{
                            title: '模型名称',
                            dataIndex: 'modelName',
                            key: 'modelName',
                            render: (_, record) => (
                              <Input
                                value={record.modelName || ''}
                                onChange={(e) => handleUpdateService(record.key, 'modelName', e.target.value)}
                                style={{ width: '100%' }}
                                placeholder="请输入模型名称"
                              />
                            )
                          }]),
                          {
                            title: '操作',
                            key: 'action',
                            width: '150px',
                            render: (_, record) => (
                              <Space>
                                <Tooltip 
                                  title={record.serviceName ? formatLoadBalanceTooltip(record) : '请先选择服务'}
                                  placement="top"
                                >
                                  <Button
                                    type="text"
                                    onClick={() => handleOpenLoadBalanceModal(record, true)}
                                    style={{ 
                                      color: record.serviceName ? '#2468F2' : '#d9d9d9', 
                                      padding: 0 
                                    }}
                                    disabled={!record.serviceName}
                                  >
                                    负载均衡
                                  </Button>
                                </Tooltip>
                                <Button
                                  type="text"
                                  onClick={() => handleRemoveService(record.key)}
                                  className={styles.deleteButton}
                                  disabled={targetServices.length <= 1}
                                >
                                  删除
                                </Button>
                              </Space>
                            )
                          }
                        ]}
                        dataSource={targetServices}
                        pagination={false}
                        rowKey="key"
                      />
                      
                      <Button
                        type="text"
                        icon={<OutlinedPlus />}
                        onClick={handleAddService}
                        style={{ marginTop: 16, color: '#2468F2',paddingLeft:0 }}
                      >
                        添加服务
                      </Button>
                    </div>
                  )}
                </Form.Item>
              </div>
            </div>
            
            {/* 认证授权模块 */}
            <div className={`${styles["module-container"]} ${styles["auth-module"]}`}>
              <div className={styles["module-title"]}>认证授权</div>
              <div className={styles["module-content"]}>
                <Form.Item label="消费者认证">
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', paddingTop:8}}>
                    <Checkbox
                      checked={authEnabled}
                      onChange={(e) => handleAuthEnabledChange(e.target.checked)}
                    >
                      开启认证
                    </Checkbox>
                    <div style={{ marginTop: 4, color: '#84878c', fontSize: 12 }}>
                      开启后，需配置可访问消费者，仅被授权的消费者可访问当前路由，也可在
                      <a 
                        style={{ color: '#2468f2', cursor: 'pointer' }}
                        onClick={() => navigate(`/instance/base/info?instanceId=${instanceId}&activeMenu=consumer-list`)}
                      >
                        消费者管理
                      </a>
                      中进行授权
                    </div>
                    
                    {authEnabled && (
                      <div className={styles.authTransferContainer}>
                          <Transfer
                            dataSource={availableConsumers.map(item => ({
                              key: item.consumerId,
                              title: item.consumerName,
                              description: item.description || '',
                            }))}
                            targetKeys={selectedConsumers}
                            onChange={handleConsumerChange}
                            leftStyle={{
                              width: '240px',
                              height: '286px'
                            }}
                            rightStyle={{
                              width: '240px',
                              height: '286px'
                            }}
                            render={item => (
                              <Tooltip title={item.title}>
                                <div style={{
                                  paddingLeft: '12px',
                                  width: '100%',
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis'
                                }}>
                                  {item.title}
                                </div>
                              </Tooltip>
                            )}
                            showSearch
                            locale={{ 
                              searchPlaceholder: '请输入消费者名称',
                              sourceNoData: (
                                <Empty
                                  imageStyle={{ height: 100 }}
                                  description="暂未创建消费者"
                                />
                              )
                            }}
                          />
                     
                      </div>
                    )}
                  </div>
                </Form.Item>
              </div>
            </div>

            {/* 高级策略模块 */}
            <div className={`${styles["module-container"]} ${styles["advanced-strategy-module"]}`}>
              <div className={styles["module-title"]}>高级策略</div>
              <div className={styles["module-content"]}>
                <Form.Item label="Token 限流"  style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', paddingTop: 8 }}>
                    <Checkbox
                      checked={tokenRateLimitEnabled}
                      onChange={(e) => handleTokenRateLimitChange(e.target.checked)}
                    >
                      开启 Token 限流
                    </Checkbox>   
                    <div style={{ marginTop: 4, color: '#84878c', fontSize: 12 }}>
                      开启后，可配置基于消费者、请求头或请求参数的 Token 限流策略，限制在指定时间窗口内的 Token 使用量
                    </div>

                    {tokenRateLimitEnabled && (
                      <div style={{ width: '100%', marginTop: 16 }}>
                        <Table
                          style={{ width: '100%' }}
                          columns={[
                            {
                              title: '限流类型',
                              dataIndex: 'matchCondition',
                              key: 'matchCondition.type',
                              width: '120px',
                              render: (matchCondition, record) => (
                                <Select
                                  value={matchCondition.type}
                                  onChange={(value) => handleUpdateRateLimitStrategy(record.key, 'matchCondition.type', value)}
                                  style={{ width: '100%' }}
                                >
                                  <Option value="consumer">按消费者</Option>
                                  <Option value="header">按请求头</Option>
                                  <Option value="query_param">按请求参数</Option>
                                </Select>
                              )
                            },
                            {
                              title: '限流条件',
                              key: 'condition',
                              width: '300px',
                              render: (_, record) => {
                                const { matchCondition } = record;
                                
                                if (matchCondition.type === 'consumer') {
                                  return (
                                    <Select
                                      value={matchCondition.value || undefined}
                                      onChange={(value) => handleUpdateRateLimitStrategy(record.key, 'matchCondition.value', value)}
                                      style={{ width: '100%' }}
                                      placeholder="请选择消费者"
                                      onFocus={() => {
                                        if (availableConsumers.length === 0) {
                                          fetchConsumers();
                                        }
                                      }}
                                    >
                                      {availableConsumers.map(consumer => (
                                        <Option key={consumer.consumerId} value={consumer.consumerName}>
                                          {consumer.consumerName}
                                        </Option>
                                      ))}
                                    </Select>
                                  );
                                } else {
                                  const isHeader = matchCondition.type === 'header';
                                  return (
                                    <div style={{ display: 'flex', gap: 8 }}>
                                      <Input
                                        value={matchCondition.key || ''}
                                        onChange={(e) => handleUpdateRateLimitStrategy(record.key, 'matchCondition.key', e.target.value)}
                                        placeholder={isHeader ? '请输入Header名称' : '请输入Query参数名'}
                                        style={{ flex: 1 }}
                                      />
                                      <Input
                                        value={matchCondition.value || ''}
                                        onChange={(e) => handleUpdateRateLimitStrategy(record.key, 'matchCondition.value', e.target.value)}
                                        placeholder={isHeader ? '请输入Header值' : '请输入Query参数值'}
                                        style={{ flex: 1 }}
                                      />
                                    </div>
                                  );
                                }
                              }
                            },
                            {
                              title: '时间单位',
                              dataIndex: 'limitConfig',
                              key: 'limitConfig.timeUnit',
                              width: '120px',
                              render: (limitConfig, record) => (
                                <Select
                                  value={limitConfig.timeUnit || undefined}
                                  onChange={(value) => handleUpdateRateLimitStrategy(record.key, 'limitConfig.timeUnit', value)}
                                  style={{ width: '100%' }}
                                  placeholder="请选择"
                                >
                                  <Option value="second">每秒</Option>
                                  <Option value="minute">每分钟</Option>
                                  <Option value="hour">每小时</Option>
                                  <Option value="day">每天</Option>
                                </Select>
                              )
                            },
                            {
                              title: 'Token数量',
                              dataIndex: 'limitConfig',
                              key: 'limitConfig.tokenAmount',
                              width: '150px',
                              render: (limitConfig, record) => (
                                <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                                  <InputNumber
                                    value={limitConfig.tokenAmount || undefined}
                                    onChange={(value) => handleUpdateRateLimitStrategy(record.key, 'limitConfig.tokenAmount', value)}
                                    style={{ width: '100%', backgroundColor: '#ffffff', borderRadius: '4px' }}
                                    min={1}
                                    max={1000000}
                                    // symmetryMode
                                    placeholder="请输入Token数量"
                                  />
                                </div>
                              )
                            },
                            {
                              title: '操作',
                              key: 'action',
                              width: '80px',
                              render: (_, record) => (
                                <Button
                                  type="text"
                                  onClick={() => handleRemoveRateLimitStrategy(record.key)}
                                  className={styles.deleteButton}
                                  disabled={rateLimitStrategies.length <= 1}
                                >
                                  删除
                                </Button>
                              )
                            }
                          ]}
                          dataSource={rateLimitStrategies}
                          pagination={false}
                          rowKey="key"
                        />
                        
                        <Button
                          type="text"
                          icon={<OutlinedPlus />}
                          onClick={handleAddRateLimitStrategy}
                          style={{ marginTop: 16, color: '#2468F2',paddingLeft:0 }}
                        >
                          添加限流策略
                        </Button>
                      </div>
                    )}
                  </div>
                </Form.Item>

                <Form.Item label="超时策略" tooltip="开启后，可配置超时时间，超过该时间后，请求将被终止"  style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', padding: '6px 0' }}>
                    <Checkbox
                      checked={timeoutPolicyEnabled}
                      onChange={(e) => setTimeoutPolicyEnabled(e.target.checked)}
                    >
                      开启超时
                    </Checkbox>
                  </div>
                </Form.Item>
                {timeoutPolicyEnabled && (
                      <div style={{ width: '100%', marginBottom: 16 }}>
                        <Form.Item
                          label="超时时间"
                          required
                          style={{ marginBottom: 0 }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <InputNumber
                              value={timeoutValue}
                              onChange={(value) => setTimeoutValue(value)}
                              onBlur={() => {
                                if (!timeoutValue || timeoutValue < 1) {
                                  setTimeoutValue(60);
                                }
                              }}
                              style={{ width: 80 }}
                              min={1}
                              max={3600}
                              placeholder="60"
                            />
                            <span style={{ marginLeft: 8}}>秒</span>
                          </div>
                        </Form.Item>
                      </div>
                    )}
             

                <Form.Item label="重试策略" tooltip="开启后，可配置重试条件和重试次数，当满足重试条件时，将执行重试操作"  style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', padding: '6px 0' }}>
                    <Checkbox
                      checked={retryPolicyEnabled}
                      onChange={(e) => setRetryPolicyEnabled(e.target.checked)}
                    >
                      开启重试
                    </Checkbox>
                  </div>
                </Form.Item>
                {retryPolicyEnabled && (
                      <div style={{ width: '100%', marginTop: 16 }}>
                        <Form.Item
                          label="重试条件"
                          required
                          style={{ marginBottom: 16 }}
                        >
                          <Select
                            mode="multiple"
                            value={retryConditions}
                            onChange={(value) => setRetryConditions(value)}
                            style={{ width: '540px' }}
                            placeholder="请选择重试条件"
                            showSelectAll={true}
                            optionLabelProp="label"
                          >
                            {retryConditionOptions.map(option => (
                              <Option key={option.value} value={option.value} label={option.label}>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                  <span style={{ color: '#262626' }}>{option.label}</span>
                                  <span style={{ color: '#8C8C8C', fontSize: '12px', marginLeft: '8px', flex: 1, textAlign: 'left' }}>
                                    {option.description}
                                  </span>
                                </div>
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                        
                        <Form.Item
                          label="重试次数"
                          required
                          style={{ marginBottom: 0 }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <InputNumber
                              value={retryAttempts}
                              onChange={(value) => setRetryAttempts(value)}
                              onBlur={() => {
                                if (!retryAttempts || retryAttempts < 1) {
                                  setRetryAttempts(3);
                                }
                              }}
                              style={{ width: 80 }}
                              min={1}
                              max={100}
                              placeholder="3"
                            />
                            <span style={{ marginLeft: 8}}>次</span>
                          </div>
                        </Form.Item>
                      </div>
                    )}
              </div>
            </div>
          </Form>
        </div>
      </div>
      
      <div className={styles['create-instance-footer']}>
        <div className={styles['create-instance-footer-content']}>
          <Button type="primary" onClick={handleSubmit} loading={submitting}>
            发布
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </div>
      </div>

      {/* 负载均衡算法弹窗 */}
      <Modal
        title="负载均衡配置"
        visible={loadBalanceModalVisible}
        onOk={handleLoadBalanceModalOk}
        onCancel={handleLoadBalanceModalCancel}
        okText="确定"
        cancelText="取消"
        width={500}
      >
        <Form
          labelAlign="left"
          labelWidth={85}
        >
          <Form.Item label="服务名称">
            <span>
              {currentEditService?.serviceName || ''}
            </span>
          </Form.Item>
          <Form.Item 
            label="负载均衡算法" 
            required
            validateStatus={modalFormErrors.loadBalanceAlgorithm ? 'error' : undefined}
            help={modalFormErrors.loadBalanceAlgorithm}
          >
            <Select
              value={currentEditService?.loadBalanceAlgorithm}
              onChange={handleModalLoadBalanceChange}
              style={{ width: '100%' }}
              placeholder="请选择负载均衡算法"
            >
              {loadBalanceOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          {/* 当选择哈希一致性时显示Hash方式 */}
          {currentEditService?.loadBalanceAlgorithm === 'consistent-hash' && (
            <Form.Item 
              label="Hash 方式" 
              required
              validateStatus={modalFormErrors.hashType ? 'error' : undefined}
              help={modalFormErrors.hashType}
            >
              <Select
                value={currentEditService?.hashType}
                onChange={handleHashTypeChange}
                style={{ width: '100%' }}
                placeholder="请选择 Hash 方式"
              >
                {hashTypeOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
          
          {/* 根据Hash方式显示对应的输入框 */}
          {currentEditService?.loadBalanceAlgorithm === 'consistent-hash' && currentEditService?.hashType === 'header' && (
            <Form.Item 
              label="请求头" 
              required
              validateStatus={modalFormErrors.hashKey ? 'error' : undefined}
              help={modalFormErrors.hashKey}
            >
              <Input
                value={currentEditService?.hashKey}
                onChange={(e) => handleHashKeyChange(e.target.value)}
                placeholder="请输入请求头"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}
          
          {currentEditService?.loadBalanceAlgorithm === 'consistent-hash' && currentEditService?.hashType === 'query_param' && (
            <Form.Item 
              label="请求参数" 
              required
              validateStatus={modalFormErrors.hashKey ? 'error' : undefined}
              help={modalFormErrors.hashKey}
            >
              <Input
                value={currentEditService?.hashKey}
                onChange={(e) => handleHashKeyChange(e.target.value)}
                placeholder="请输入请求参数"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}
          
          {currentEditService?.loadBalanceAlgorithm === 'consistent-hash' && currentEditService?.hashType === 'cookie' && (
            <Form.Item 
              label="Cookie名称" 
              required
              validateStatus={modalFormErrors.hashKey ? 'error' : undefined}
              help={modalFormErrors.hashKey}
            >
              <Input
                value={currentEditService?.hashKey}
                onChange={(e) => handleHashKeyChange(e.target.value)}
                placeholder="请输入Cookie名称"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default RouteCreate;