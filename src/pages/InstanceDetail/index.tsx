import React, {useEffect, useMemo, useRef, useState, lazy, Suspense} from 'react';
import {useNavigate, useLocation} from 'react-router-dom';
import {getQueryParams, useRegion, useFrameworkContext} from '@baidu/bce-react-toolkit';
import {useRequest} from 'ahooks';
import {Layout, Menu, Tabs, Button, Table, Space, Tooltip, Drawer, Form, Input, Radio, Alert, Empty, Tag, Popconfirm, toast, Modal, Checkbox, Popover} from 'acud';
import {OutlinedRefresh, OutlinedPlusNew, OutlinedCheckCircle, OutlinedInfoCircle} from 'acud-icon';
import type {TablePaginationConfig} from 'acud/lib/table';
import ServiceSourceRadio from '@/components/ServiceSourceRadio';
import CopyDataWhenHover from '@/components/CopyDataWhenHover';
import { getRegionDisplayName } from '@/constants/regions';

import {
  getRegistrationInstance, 
  getInstanceRelatedClusters,
  getVpcClusters,
  associateCluster,
  removeCluster
} from '@/apis/instance';
import DetailPage from '@/components/DetailPage';
import BaseInfo from '@/pages/InstanceBaseInfo';
import {RegistractionStatusDict} from '@/utils/enums';
import urls from '@/utils/urls';
import {formatTime} from '@/utils/utils';

import styles from './index.module.less';

const {Sider, Content} = Layout;
const {TabPane} = Tabs;

// 使用lazy导入消费者列表组件
const ConsumerList = lazy(() => import('@/pages/ConsumerList'));
// 使用lazy导入推理服务组件
const InferenceService = lazy(() => import('@/pages/InferenceService'));
// 引入RouteList组件
const RouteList = lazy(() => import('@/pages/RouteList'));
// 使用lazy导入IP黑白名单组件
const IpBlackWhiteList = lazy(() => import('@/pages/IpBlackWhiteList'));

const InstanceDetail = () => {
  const {instanceId, activeMenu} = getQueryParams();
  
  // 调试 - 显示instanceId
  // console.log('InstanceDetail组件被加载，instanceId:', instanceId, 'activeMenu:', activeMenu);
  
  const [detail, setDetail] = useState<any>({});
  const {region} = useRegion();
  const {frameworkData} = useFrameworkContext();
  const navigate = useNavigate();
  const isFirstRun = useRef(true);
  // 如果URL中有activeMenu参数，则使用它；否则默认为'instance-detail'
  const [activeMenuItem, setActiveMenuItem] = useState(activeMenu || 'instance-detail');
  const [activeTab, setActiveTab] = useState('basic-info');
  const [activeRelationTab, setActiveRelationTab] = useState('cluster');
  
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [clusterForm] = Form.useForm();
  
  // K8S Ingress监听相关状态变量
  const [ingressEnabled, setIngressEnabled] = useState(false);
  const [ingressClassesType, setIngressClassesType] = useState('all');
  const [namespacesType, setNamespacesType] = useState('all');

  const {run: requestDetail} = useRequest(
    () => {
      // console.log('请求实例详情，instanceId:', instanceId, '地域:', region);
      return getRegistrationInstance(instanceId, region);
    },
    {
      ready: !!instanceId,
      onSuccess: (res) => {
        // console.log('原始响应数据:', res);
        
        // 处理API响应
        let result = {};
        
        // 判断API返回结构，根据接口文档处理
        if (res?.result) {
          // 新格式，按照接口文档处理
          const data = res.result || {};
          
          // 记录原始数据
          // console.log('接口返回原始data数据:', data);
          
          result = {
            data: {
              // 基本信息
              instanceId: data.instanceId || '', // 实例ID
              name: data.name || '', // 实例名称
              ingressStatus: data.ingressStatus || '', // 实例状态
              region: data.region || '', // 所属地域
              namespace: data.namespace || '', // 命名空间
              description: data.description || '', // 实例描述
              createTime: data.createTime || '', // 创建时间
              
              // 网络相关
              vpcId: data.vpcId || '', // VPC ID
              vpcCidr: data.vpcCidr || '', // VPC网段
              subnetId: data.subnetId || '', // 子网ID
              
              // 实例规格
              gatewayType: data.gatewayType || '', // 网关规格
              replicas: data.replicas || 0, // 节点数量
              
              // 接入地址相关
              internalIP: data.internalIP || '', // 内网地址
              externalIP: data.externalIP || '', // 公网地址
              publicAccessible: data.publicAccessible || false, // 是否可公网访问
              deleteProtection: data.deleteProtection || false, // 是否开启删除保护
              
              // 集群信息
              ClusterInfo: data.ClusterInfo || {}, // 集群信息
              
              // 负载均衡信息
              loadBalanceInfo: data.loadBalanceInfo || {} // 负载均衡信息
            }
          };
        } else if ((res as any)?.data) {
          // 兼容其他格式
          result = {
            data: (res as any).data || {}
          };
        }
        
        // console.log('处理后的实例详情数据:', result);
        // 将处理后的数据设置到状态
        setDetail(result);
      },
      onError: (error) => {
        console.error('获取实例详情失败:', error);
      }
    }
  );

  // 将状态字符串映射为数字状态码
  const mapStatusValue = (statusStr) => {
    const statusMap = {
      'running': 2, // 运行中
      'creating': 1, // 创建中
      'initializing': 0, // 初始化
      'adjusting': 3, // 调整中
      'releasing': 4, // 释放中
      'error': 5, // 运行异常
      'failed': 6  // 创建失败
    };
    return statusMap[statusStr] || 0;
  };

  // 关联集群抽屉表单项中的所属VPC显示
  const renderVpcField = () => {
    return detail?.data?.vpcId || '-';
  };

  useEffect(() => {
    if (isFirstRun.current) {
      isFirstRun.current = false;
      return;
    }
    navigate(urls.registrationList);
  }, [region]);

  // 左侧菜单数据
  const menuItems = [
    {
      key: 'instance-detail',
      label: '实例详情',
      url: urls.registrationBaseInfo
    },
    {
      key: 'inference-service',
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
          <span>推理服务</span>
          <Tag style={{
            height: '16px', 
            color: '#F33E3E', 
            margin: 0, 
            backgroundColor: 'transparent',
            border: '1px solid #F33E3E',
            padding: '0 4px',
            fontSize: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>HOT</Tag>
        </div>
      ),
      url: '#'
    },
    {
      key: 'route-config', // 新增路由配置菜单项
      label: '路由配置',
      url: urls.routeConfig
    },
    {
      key: 'consumer-list',
      label: '消费者管理',
      url: urls.consumerList
    },
    {
      key: 'access-control',
      label: '访问控制',
      url: urls.ipBlackWhiteList
    }
  ];

  useEffect(() => {
    if (activeMenuItem === 'instance-detail' && activeTab === 'instance-relation' && activeRelationTab === 'cluster') {
      fetchInstanceClusters();
    }
  }, [activeMenuItem, activeTab, activeRelationTab, instanceId]);

  // 获取实例关联的集群列表
  const [clusterListData, setClusterListData] = useState<any>({});
  const [clusterListLoading, setClusterListLoading] = useState(false);
  const [clusterPagination, setClusterPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    onChange: (page: number, pageSize?: number) => {
      console.log('分页变化', page, pageSize);
      fetchInstanceClusters(page, pageSize || 10);
    }
  });
  
  // 获取实例关联的集群列表
  const fetchInstanceClusters = (pageNo = 1, pageSize = 10) => {
    if (!instanceId) return;

    // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
    const instanceRegion = detail?.data?.region || region;
    // console.log('请求实例关联的集群列表, instanceId:', instanceId, '实例地域:', instanceRegion);
    setClusterListLoading(true);

    getInstanceRelatedClusters(instanceId, {
      pageNo,
      pageSize,
      orderBy: 'relationTime',
      order: 'desc'
    }, instanceRegion)
      .then(res => {
        // console.log('获取实例关联的集群列表成功:', res);
        // console.log('接口返回数据结构:', JSON.stringify(res, null, 2));
        
        // 这里强制用any类型处理res，以绕过TypeScript的类型检查
        const response = res as any;
        
        // 接口返回格式为 { page: {...} }，page字段直接在res对象中
        if (response?.success && response?.page) {
          const pageData = response.page;
          // console.log('解析到的page数据:', pageData);
          // console.log('集群列表数据:', pageData.result);
          
          // 更新数据和分页信息
          setClusterListData(pageData);
          setClusterPagination({
            ...clusterPagination,
            current: pageData.pageNo || 1,
            pageSize: pageData.pageSize || 10,
            total: pageData.totalCount || 0
          });
        } else {
          console.error('接口返回数据结构不符合预期:', res);
          setClusterListData({result: []});
        }
      })
      .catch(error => {
        console.error('获取实例关联的集群列表失败:', error);
      })
      .finally(() => {
        setClusterListLoading(false);
      });
  };
  
  // 抽屉相关状态
  const [clusterDrawerVisible, setClusterDrawerVisible] = useState(false);
  const [vpcClusters, setVpcClusters] = useState<any[]>([]);
  const [vpcClustersLoading, setVpcClustersLoading] = useState(false);
  const [clusterAssociateForm] = Form.useForm();
  
  // 查询同VPC下的集群
  const fetchVpcClusters = (vpcId: string) => {
    if (!vpcId) {
      console.log('未提供vpcId，无法查询集群');
      return;
    }

    // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
    const instanceRegion = detail?.data?.region || region;
    console.log('查询同VPC中的CCE集群, vpcId:', vpcId, '实例地域:', instanceRegion);
    setVpcClustersLoading(true);

    getVpcClusters(vpcId, instanceRegion)
      .then(res => {
        // console.log('获取同VPC中的CCE集群成功:', res);
        if (res?.success && res?.result) {
          setVpcClusters(res.result || []);
        } else {
          setVpcClusters([]);
        }
      })
      .catch(error => {
        // console.error('获取同VPC中的CCE集群失败:', error);
        setVpcClusters([]);
      })
      .finally(() => {
        setVpcClustersLoading(false);
      });
  };

  // 选中集群的ID
  const [selectedClusterId, setSelectedClusterId] = useState<string>('');
  
  // 关联集群
  const handleAssociateCluster = async () => {
    try {
      const values = await clusterAssociateForm.validateFields();
      // console.log('提交关联集群表单:', values);
      
      // 检查是否选择了集群
      if (!selectedClusterId) {
        // console.error('未选择要关联的集群');
        // 显示错误提示
        clusterAssociateForm.setFields([
          {
            name: 'clusterId',
            errors: ['请选择需要关联的集群']
          }
        ]);
        return;
      }
      
      setVpcClustersLoading(true); // 显示加载状态
      
      // 构建请求数据
      const requestData = {
        clusters: [
          {
            clusterId: values.clusterId,
            clusterName: values.clusterName
          }
        ],
        remark: values.remark,
        ingressSettings: {
          enableIngress: values.enableIngress || false,
          enableAllIngressClasses: values.ingressClassesType === 'all',
          enableAllNamespaces: values.namespacesType === 'all',
          ingressClasses: values.ingressClassesType === 'custom' ? [values.customIngressClass] : [],
          watchNamespaces: []
        }
      };
      
      // console.log('关联集群请求数据:', requestData);

      // 使用实例的地域信息
      const instanceRegion = detail?.data?.region || region;
      associateCluster(instanceId, requestData, instanceRegion)
        .then(res => {
          // console.log('关联集群成功:', res);
          if (res?.success) {
            // 成功提示
            toast.success({
              message: `关联集群 ${values.clusterName} 成功`,
              duration: 3
            });
            // 关闭抽屉并刷新列表
            setClusterDrawerVisible(false);
            fetchInstanceClusters(); // 刷新集群列表
            // 重置表单
            clusterAssociateForm.resetFields();
          }
        })
        .catch(error => {
          console.error('关联集群失败:', error);
          // 这里可以添加toast提示
        })
        .finally(() => {
          setVpcClustersLoading(false);
        });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };
  
  // 打开关联集群抽屉
  const openClusterDrawer = () => {
    // console.log('打开关联集群抽屉');
    
    // 重置表单和选中状态
    clusterAssociateForm.resetFields();
    setSelectedClusterId('');
    
    // 重置Ingress相关状态
    setIngressEnabled(false);
    setIngressClassesType('all');
    setNamespacesType('all');
    
    // 设置初始表单数据
    clusterAssociateForm.setFieldsValue({
      instance: `${detail?.data?.name || '-'} (${detail?.data?.instanceId || '-'})`,
      vpc: detail?.data?.vpcId || '-',
      clusterId: '',
      clusterName: '',
      enableIngress: false,
      ingressClassesType: 'all',
      namespacesType: 'all'
    });
    
    // 获取同VPC的集群列表
    if (detail?.data?.vpcId) {
      fetchVpcClusters(detail.data.vpcId);
    }
    
    // 显示抽屉
    setClusterDrawerVisible(true);
  };
  
  // 关闭关联集群抽屉
  const closeClusterDrawer = () => {
    setClusterDrawerVisible(false);
  };
  
  // 实例关联的集群列表列定义
  const relatedClusterColumns = [
    {
      title: '集群名称/ID',
      key: 'clusterName',
      width: 220,
      render: (_, record) => (
        <>
          <Tooltip title={record.clusterName} placement="topLeft">
            <div>
              <a
                href={`https://console.bce.baidu.com/cce/#/cce/cluster/detail?clusterUuid=${record.clusterId}&clusterName=${encodeURIComponent(record.clusterName)}`}
                target="_blank"
                rel="noreferrer"
                style={{
                  color: '#2468f2',
                  cursor: 'pointer',
                  display: 'inline-block',
                  maxWidth: 200,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {record.clusterName}
              </a>
            </div>
          </Tooltip>
          
            <CopyDataWhenHover copyValue={record.clusterId} />
        
        </>
      )
    },
    {
      title: '集群状态',
      dataIndex: 'status',
      key: 'status',
      width: 110,
      render: (status) => {
        const statusMap = {
          'RUNNING': { text: '运行中', iconClass: 'circle status-success' },
          'CREATING': { text: '创建中', iconClass: 'circle status-processing' },
          'CREATING_FAILED': { text: '创建失败', iconClass: 'circle status-error' },
          'ERROR': { text: '访问异常', iconClass: 'circle status-error' },
          'DELETING': { text: '删除中', iconClass: 'circle status-active' },
          'DELETED': { text: '已删除', iconClass: 'circle status-inactive' },
          'DELETED_FAILED': { text: '删除失败', iconClass: 'circle status-error' },
          'MASTER_UPGRADING': { text: '升级中', iconClass: 'circle status-processing' },
          'MASTER_UPGRADE_FAILED': { text: '升级失败', iconClass: 'circle status-error' },
        };
        const currentStatus = statusMap[status] || { text: status || '-', iconClass: 'circle status-warning' };
        return (
          <Tag
            color="transparent"
            className="table-status"
            style={{ paddingLeft: 0 }}
            icon={<span className={currentStatus.iconClass} />}
          >
            {currentStatus.text}
          </Tag>
        );
      }
    },
    {
      title: '关联状态',
      dataIndex: 'relationStatus',
      key: 'relationStatus',
      width: 110,
      render: (relationStatus) => {
        const statusMap = {
          'ASSOCIATING': { text: '关联中', iconClass: 'circle status-processing' },
          'ASSOCIATED': { text: '已关联', iconClass: 'circle status-success' },
          'ERROR': { text: '关联失败', iconClass: 'circle status-error' }
        };
        const current = statusMap[relationStatus] || { text: relationStatus || '-', iconClass: 'circle status-warning' };
        return (
          <Tag
            color="transparent"
            className="table-status"
            style={{ paddingLeft: 0 }}
            icon={<span className={current.iconClass} />}
          >
            {current.text}
          </Tag>
        );
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 220,
      render: (text: string) => (
        <Tooltip title={text || '-'}>
          <span
            style={{
              display: 'inline-block',
              maxWidth: 200, // 适当比列宽小一些，视实际宽度调整
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              verticalAlign: 'middle',
            }}
          >
            {text || '-'}
          </span>
        </Tooltip>
      )
    },
    {
      title: 'Ingress 监听',
      key: 'ingressSettings',
      width: 140,
      render: (_, record) => {
        const ingressSettings = record.ingressSettings || {};
        const isEnabled = ingressSettings.enableIngress;
        
        if (!isEnabled) {
          return <Tag color="inactive">未开启</Tag>;
        }
        
        return (
          <Popover
            placement="top"
            content={
              <div style={{ width: 300 }}>
                <div style={{ display: 'flex', borderBottom: '1px solid #f0f0f0', padding: '8px 0', fontWeight: 500 }}>
                  <div style={{ width: '50%', color: '#151B26' }}>配置</div>
                  <div style={{ width: '50%', color: '#151B26' }}>详情</div>
                </div>
                <div style={{ display: 'flex', padding: '8px 0', borderBottom: '1px solid #f5f5f5' }}>
                  <div style={{ width: '50%', color: '#5C6066' }}>指定IngressClass</div>
                  <div style={{ width: '50%', color: '#151B26' }}>
                    {ingressSettings.enableAllIngressClasses 
                      ? '全部同步' 
                      : ingressSettings.ingressClasses && ingressSettings.ingressClasses.length > 0 
                        ? ingressSettings.ingressClasses.join(', ') 
                        : '-'
                    }
                  </div>
                </div>
                <div style={{ display: 'flex', padding: '8px 0' }}>
                  <div style={{ width: '50%', color: '#5C6066' }}>指定命名空间</div>
                  <div style={{ width: '50%', color: '#151B26' }}>
                    {ingressSettings.enableAllNamespaces 
                      ? '全部同步' 
                      : ingressSettings.watchNamespaces && ingressSettings.watchNamespaces.length > 0 
                        ? ingressSettings.watchNamespaces.join(', ') 
                        : '-'
                    }
                  </div>
                </div>
              </div>
            }
            title="Ingress 监听配置"
          >
            <Tag color="success-status">已开启</Tag>
          </Popover>
        );
      }
    },
    {
      title: '关联时间',
      dataIndex: 'relationTime',
      key: 'relationTime',
      width:160
    },
    {
      title: '最近更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width:160
    },
    {
      title: '操作',
      key: 'action',
      width:120,
      render: (_, record) => (
        <a
          style={{ cursor: 'pointer', color: '#2468f2' }}
          onClick={() => {
            Modal.confirm({
              title: '确定要解除关联吗？',
              content: `解除关联后，该网关实例将不再与集群 "${record.clusterName}" 关联。`,
              onOk: () => handleRemoveCluster(record.clusterId),
              onCancel() {}
            });
          }}
        >解除关联</a>
      )
    }
  ];
  
  // VPC集群列表列定义
  const vpcClusterColumns = [
    {
      title: '集群名称/ID',
      key: 'clusterName',
      render: (_, record) => (
        <Tooltip title={record.clusterName} placement="topLeft">
          <div>
            <a
              href={`https://console.bce.baidu.com/cce/#/cce/cluster/detail?clusterUuid=${record.clusterId}&clusterName=${encodeURIComponent(record.clusterName)}`}
              target="_blank"
              rel="noreferrer"
              style={{ color: '#2468f2', cursor: 'pointer', display: 'inline-block', maxWidth: '100%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
            >
              {record.clusterName}
            </a>
          </div>
          <div style={{ color: '#151B26', fontSize: '12px' }}>{record.clusterId}</div>
        </Tooltip>
      )
    },
    {
      title: '集群状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'RUNNING': { text: '运行中', iconClass: 'circle status-success' },
          'CREATING': { text: '创建中', iconClass: 'circle status-processing' },
          'CREATING_FAILED': { text: '创建失败', iconClass: 'circle status-error' },
          'ERROR': { text: '异常', iconClass: 'circle status-error' },
          'DELETING': { text: '删除中', iconClass: 'circle status-active' },
          'DELETED': { text: '已删除', iconClass: 'circle status-inactive' },
          'DELETED_FAILED': { text: '删除失败', iconClass: 'circle status-error' },
          'MASTER_UPGRADING': { text: '升级中', iconClass: 'circle status-processing' },
          'MASTER_UPGRADE_FAILED': { text: '升级失败', iconClass: 'circle status-error' },     
        };
        const currentStatus = statusMap[status] || { text: status || '-', iconClass: 'circle status-warning' };
        return (
          <Tag
            color="transparent"
            className="table-status"
            style={{paddingLeft: 0}}
            icon={<span className={currentStatus.iconClass} />}
          >
            {currentStatus.text}
          </Tag>
        );
      }
    },
    {
      title: 'VPC网段',
      dataIndex: 'vpcCidr',
      key: 'vpcCidr'
    },
    {
      title: '地域',
      dataIndex: 'region',
      key: 'region',
      render(value: string) {
        return getRegionDisplayName(value);
      }
    }
  ];
  
  // 右侧内容区Tab数据
  const tabPanes = [
    {
      key: 'basic-info',
      tab: '基本信息',
      content: (
        <BaseInfo
          detail={detail}
          requestDetail={requestDetail}
          instanceId={instanceId}
        />
      )
    },
    {
      key: 'instance-relation',
      tab: '关联信息',
      content: (
        <div className={styles['related-info-container']}>
          {/* <Tabs activeKey={activeRelationTab} onChange={setActiveRelationTab}> */}
            {/* <TabPane tab="关联容器集群" key="cluster"> */}
              <div className={styles['cluster-header']}>
                <Tooltip title={clusterListData.totalCount >= 1 ? '当前实例至多关联 1 个集群' : ''}>
                  <span>
                    <Button
                      type="primary"
                      icon={<OutlinedPlusNew />}
                      onClick={openClusterDrawer}
                      disabled={clusterListData.totalCount >= 1}
                    >
                      关联容器集群
                    </Button>
                  </span>
                </Tooltip>
                <Button 
                  icon={<OutlinedRefresh />}
                  onClick={() => fetchInstanceClusters()}
                  style={{ marginLeft: 'auto' }}
                />
              </div>
              <Table
                columns={relatedClusterColumns}
                dataSource={clusterListData?.result || []}
                loading={clusterListLoading}
                rowKey="clusterId"
                pagination={clusterPagination}
                locale={{
                  emptyText: (
                    <Empty
                      description={
                        <span>
                          暂未关联集群。
                          <a onClick={openClusterDrawer} style={{ marginLeft: '4px', cursor: 'pointer' }}>
                            <OutlinedPlusNew style={{ marginRight: '4px' }} />
                            关联集群
                          </a>
                        </span>
                      }
                    />
                  )
                }}
              />
              
              {/* 关联集群弹窗 */}
              <Modal
                title="关联容器集群"
                width={840}
                className='modalHeight'
                visible={clusterDrawerVisible}
                onCancel={closeClusterDrawer}
                footer={
                  <div style={{ textAlign: 'right' }}>
                    <Button onClick={closeClusterDrawer} style={{ marginRight: 8 }}>
                      取消
                    </Button>
                    <Button type="primary" onClick={handleAssociateCluster}>
                      确定
                    </Button>
                  </div>
                }
              >
                <Alert 
                  message="将与网关网络互通的集群关联，使网关可以直接访问集群内信息（关联时请您确保目标集群已创建有效的 kubeconfig）" 
                  type="info" 
                  showIcon 
                  className={styles['alert-message']}
                />
                <Form inputMaxWidth={'100%'}
                  form={clusterAssociateForm}
                  layout="horizontal"
                  labelCol={{ span: 4 }}
                  // wrapperCol={{ span: 20 }}
                  labelAlign="left"
                >
                  {/* 所属实例 */}
                  <Form.Item label="所属实例/ID" name="instance">
                    <div className={styles['readonly-field']}>
                      {detail?.data?.name || '-'} ({detail?.data?.instanceId || '-'})
                    </div>
                  </Form.Item>

                  {/* 所属VPC */}
                  <Form.Item label="所属VPC" name="vpc">
                    <div className={styles['readonly-field']}>
                      {detail?.data?.vpcId ? (
                        <a
                          href={`https://console.bce.baidu.com/network/#/vpc/instance/detail?vpcId=${detail.data.vpcId}`}
                          target="_blank"
                          rel="noreferrer"
                          style={{ color: '#2468f2', cursor: 'pointer' }}
                        >
                          {detail.data.vpcId}
                        </a>
                      ) : (
                        '-'
                      )}
                    </div>
                  </Form.Item>

                  {/* 备注 */}
                  <Form.Item 
                    label="备注" 
                    name="remark"
                    
                    rules={[{ max: 64, message: '备注最多 64 个字符' }]}
                  >
                    <Input limitLength={64} style={{width:'400px'}}
                      placeholder="请输入关联集群的备注信息，最多 64 个字符" 
                    
                    />
                  </Form.Item>

                  {/* 服务来源 */}
                  <Form.Item label="集群来源" name="serviceSource" initialValue="CCE">
                    <ServiceSourceRadio description="高度可扩展的高性能容器管理服务" />
                  </Form.Item>

                  {/* CCE集群表格 */}
                  <Form.Item 
                    label="CCE集群" 
                  required
                    rules={[{  message: '请选择需要关联的集群' }]}
                  >
                    <Table
                      rowSelection={{
                        type: 'radio',
                        selectedRowKeys: selectedClusterId ? [selectedClusterId] : [],
                        onChange: (selectedRowKeys) => {
                          const clusterId = selectedRowKeys[0] as string;
                          setSelectedClusterId(clusterId);
                          
                          // 找到选中的集群数据
                          const selectedCluster = vpcClusters.find(item => item.clusterId === clusterId);
                          if (selectedCluster) {
                            // 设置表单字段
                            clusterAssociateForm.setFieldsValue({
                              clusterId: selectedCluster.clusterId,
                              clusterName: selectedCluster.clusterName
                            });
                          }
                        }
                      }}
                      columns={vpcClusterColumns}
                      dataSource={vpcClusters}
                      loading={vpcClustersLoading}
                      rowKey="clusterId"
                      pagination={{ 
                        pageSize: 10,
                        showSizeChanger: false,
                        showTotal: (total) => `共 ${total} 条`
                      }}
                      className="cluster-table-fixed-width"
                      locale={{ 
                        emptyText: (
                          <Empty
                            style={{ 
                              margin: '40px 0',
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}
                            description={
                              <span>
                                暂无集群，
                                可以 <a href='https://console.bce.baidu.com/cce/#/cce/cluster/list' target='_blank'>去创建 CCE 集群</a>
                              </span>
                            }
                          />
                        )
                      }}
                     
                      onRow={(record) => ({
                        onClick: () => {
                          setSelectedClusterId(record.clusterId);
                          clusterAssociateForm.setFieldsValue({
                            clusterId: record.clusterId,
                            clusterName: record.clusterName
                          });
                        }
                      })}
                    />
                  </Form.Item>

                  {/* 隐藏字段，用于存储选中的集群信息 */}
                  <Form.Item name="clusterId" hidden>
                    <Input />
                  </Form.Item>
                  <Form.Item name="clusterName" hidden>
                    <Input />
                  </Form.Item>

                  {/* 监听K8S Ingress字段 */}
                  <Form.Item
                    label="监听K8S Ingress"
                    name="enableIngress"
                    valuePropName="checked"
                  >
                    <Checkbox 
                      onChange={(e) => {
                        setIngressEnabled(e.target.checked);
                        clusterAssociateForm.setFieldsValue({
                          enableIngress: e.target.checked,
                          ingressClassesType: 'all',
                          namespacesType: 'all'
                        });
                      }}
                    >
                      开启监听
                    </Checkbox>
                  </Form.Item>

                  {/* 当开启监听K8S Ingress时显示的选项 */}
                  {ingressEnabled && (
                    <>
                      <Form.Item 
                        label="指定IngressClass" 
                        name="ingressClassesType"
                        initialValue="all"
                      >
                        <Radio.Group 
                          onChange={(e: any) => {
                            setIngressClassesType(e.target.value);
                          }}
                        >
                          <Radio.Button value="all">全部同步</Radio.Button>
                          <Radio.Button value="custom">自定义范围</Radio.Button>
                        </Radio.Group>
                      </Form.Item>

                      {ingressEnabled && ingressClassesType === 'custom' && (
                        <Form.Item 
                          label="IngressClass" 
                          name="customIngressClass"
                          rules={[{ required: true, message: '请输入IngressClass名称' }]}
                        >
                          <Input placeholder="请输入名称，目前仅支持一个" style={{ width: '300px' }} />
                        </Form.Item>
                      )}

                      <Form.Item 
                        label="指定命名空间" 
                        name="namespacesType"
                        initialValue="all"
                      >
                        <Radio.Group>
                          <Radio.Button value="all">全部同步</Radio.Button>
                        </Radio.Group>
                      </Form.Item>
                    </>
                  )}
                </Form>
              </Modal>
            {/* </TabPane> */}
            {/* 目前暂时无关联注册中心此能力，先隐藏 */}
            {/* <TabPane tab="关联注册中心" key="registry">
              <Empty
                description="功能开发中，敬请期待"
              />
            </TabPane> */}
          {/* </Tabs> */}
        </div>
      )
    }
  ];

  // 获取当前菜单对应的内容
  const getContentByMenuKey = (key) => {
    switch (key) {
      case 'instance-detail':
        return (
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            {tabPanes.map((pane) => (
              <TabPane tab={pane.tab} key={pane.key}>
                {pane.content}
              </TabPane>
            ))}
          </Tabs>
        );
      case 'inference-service':
        return (
          <Suspense fallback={<div></div>}>
            <InferenceService />
          </Suspense>
        );
      case 'route-config': // 处理路由配置菜单项
        return (
          <Suspense fallback={<div></div>}> 
            <RouteList
              detail={detail}
              requestDetail={requestDetail}
              instanceId={instanceId || ''}
            />
          </Suspense>
        );
      case 'consumer-list':
        return (
          <Suspense fallback={<div></div>}>
            <ConsumerList
              detail={detail}
              requestDetail={requestDetail}
              instanceId={instanceId || ''}
            />
          </Suspense>
        );
      case 'access-control':
        return (
          <Suspense fallback={<div></div>}>
            <IpBlackWhiteList
              instanceId={instanceId || ''}
              detail={detail}
            />
          </Suspense>
        );
      default:
        return null;
    }
  };

  // 状态到文本和颜色class的映射
  const statusMap = {
    running: { text: '运行中', colorClass: 'status-success' },
    error: { text: '异常', colorClass: 'status-error' },
    creating: { text: '创建中', colorClass: 'status-warning' }, 
    releasing: { text: '释放中', colorClass: 'status-warning' },
    failed: { text: '失败', colorClass: 'status-error' },
    adjusting: { text: '调整中', colorClass: 'status-warning' },
    initializing: { text: '初始化', colorClass: 'status-warning' },
    // 可根据实际补充
  };

  const [text, styleClass] = useMemo(() => {
    const status = detail?.data?.ingressStatus || '';
    const info = statusMap[status] || { text: status || '-', colorClass: 'status-unknown' };
    return [info.text, info.colorClass];
  }, [detail]);
  
  // 获取实例名称
  const getInstanceName = useMemo(() => {
    // 优先使用转换后的name字段
    if (detail?.data?.name) {
      return detail.data.name;
    }
    // 其次使用ClusterInfo中的名称
    else if (detail?.ClusterInfo?.clusterName) {
      return detail.ClusterInfo.clusterName;
    }
    return '未命名实例';
  }, [detail]);
  
  // 获取实例ID
  const getInstanceId = useMemo(() => {
    // console.log('计算getInstanceId, 当前instanceId:', instanceId);
    // console.log('detail对象:', detail);
    
    // 优先使用URL参数中的instanceId
    if (instanceId) {
      return instanceId;
    }
    // 其次使用转换后的id字段
    else if (detail?.data?.instanceId) {
      return detail.data.instanceId;
    }
    // 再次使用id
    else if (detail?.id) {
      return detail.id;
    }
    // 最后使用ingressId
    else if (detail?.ingressId) {
      return detail.ingressId;
    }
    return '';
  }, [detail, instanceId]);

  const handleMenuClick = (key) => {
    console.log('点击菜单项:', key);
    setActiveMenuItem(key);
    
    // 当点击实例详情菜单项时，将标签重置为基本信息
    if (key === 'instance-detail') {
      setActiveTab('basic-info');
    }
    
    // 更新URL，添加或更新activeMenu参数
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.hash.split('?')[1] || '');
    params.set('activeMenu', key);
    params.set('instanceId', instanceId); // 确保instanceId参数存在
    
    // 如果不是实例详情菜单项，则删除activeTab参数；否则保留当前activeTab
    if (key !== 'instance-detail') {
      params.delete('activeTab');
    } else if (activeTab) {
      params.set('activeTab', activeTab);
    }
    
    // 构建新的URL
    const hashPart = currentUrl.hash.split('?')[0];
    const newHash = `${hashPart}?${params.toString()}`;
    
    // 更新URL，但不触发页面重新加载
    window.history.replaceState(null, '', `#${newHash.substring(1)}`);
    
   // console.log('菜单点击，更新URL:', newHash);
  };

  // 创建空的panesData数组，解决isCustomContent为true时panesData报错的问题
  const emptyPanesData = [];

  const siderStyle = {
    background: '#fff',
    boxShadow: 'none'
  };

  // 处理解除关联集群
  const handleRemoveCluster = (clusterId) => {
   // console.log('解除关联集群, instanceId:', instanceId, 'clusterId:', clusterId);
    // 返回接口 Promise，以支持 Modal.confirm onOk loading
    // 使用实例的地域信息
    const instanceRegion = detail?.data?.region || region;
    return removeCluster(instanceId, clusterId, instanceRegion)
      .then(res => {
       // console.log('解除关联集群成功:', res);
        if (res?.success) {
          // 显示成功提示
          toast.success({
            message: '已移除集群',
            duration: 3
          });
          // 刷新列表
          fetchInstanceClusters();
        }
      })
      .catch(error => {
        console.error('解除关联集群失败:', error);
        // 显示错误提示
        toast.error({
          message: '解除关联失败',
          description: error?.message || '请稍后重试'
        });
      });
  };

  const location = useLocation();

  // 监听URL查询参数变化
  useEffect(() => {
    //console.log('URL变化，检查查询参数:', location.search);
    
    const params = new URLSearchParams(location.search);
    const newActiveMenu = params.get('activeMenu') || 'instance-detail';
    const newActiveTab = params.get('activeTab');

    //console.log('解析查询参数 - activeMenu:', newActiveMenu, 'activeTab:', newActiveTab);

    // 设置菜单选中项
    setActiveMenuItem(newActiveMenu);

    // 如果在实例详情页，根据activeTab参数切换标签页
    if (newActiveMenu === 'instance-detail') {
      if (newActiveTab) {
       // console.log('设置activeTab为:', newActiveTab);
        setActiveTab(newActiveTab);
      } else {
       // console.log('默认设置activeTab为basic-info');
        setActiveTab('basic-info');
      }
    }
  }, [location.search, location.key]);

  return (
    <div className={styles['instance-detail-container']}>
      <DetailPage
        mode="vertical"
        headerName={`${getInstanceName}（${getInstanceId}）`}
        backUrl="#/instance/list"
        dataResponse={detail} 
        statusText={text}
        statusClassName={styleClass}
        isCustomContent={true}
        panesData={emptyPanesData}
      >
        <div className={styles['instance-detail-layout']}>
          <Sider 
            width={160} 
            className={styles['instance-detail-sider']}
            theme="light"
            style={siderStyle}
          >
            <Menu
              mode="inline"
              selectedKeys={[activeMenuItem]}
              className={styles['instance-detail-menu']}
              onSelect={({key}) => handleMenuClick(key)}
              style={{ height: '100%', borderRight: 'none', boxShadow: 'none' }}
            >
              {menuItems.map((item) => (
                <Menu.Item key={item.key}>{item.label}</Menu.Item>
              ))}
            </Menu>
          </Sider>
          <Content className={styles['instance-detail-content']}>
            {getContentByMenuKey(activeMenuItem)}
          </Content>
        </div>
      </DetailPage>
    </div>
  );
};
export default InstanceDetail;
