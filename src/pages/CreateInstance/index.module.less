.create-instance-wrap {
  background-color: #f5f5f5;
  width: 100%;
  height: 100%;
  overflow: auto;
  
  // 覆盖页面头部的样式
  :global(.page-header) {
    display: none !important;
  }
  
  :global(.page-content) {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  // 页面头部
  .create-instance-header {
    height: 50px;
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 0 16px;
  }
  
  // 内容区域
  .create-instance-content {
    background-color: #fff;
    border-radius: 6px;
    margin: 16px;
    padding: 24px;
    
    // 模块容器样式
    .module-container {
      margin-bottom: 24px;
      
      .module-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #151a26;
        position: relative;
        
        &::before {
          content: "";
          position: absolute;
          left: -8px;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background-color: #2468f2;
          border-radius: 2px;
        }
      }
      
      .module-content {
        :global {
          .acud-form-item {
            margin-bottom: 16px;
          }
        }
      }
    }
    
    // 网络配置模块样式
    .network-module {
      .vpc-selection {
        display: flex;
        align-items: center;
        
        button {
          margin-left: 8px;
        }
      }
      
      .subnet-ip-count {
        color: #84878c;
        font-size: 12px;
        margin-top: 4px;
      }
    }
    
    // 关联信息模块样式
    .related-info-module {
      // 添加强制宽度样式
      :global(.cluster-table-fixed-width) {
        width: 700px !important;
        min-width: 700px !important;
        max-width: 700px !important;
        
        // 表格列样式
        .cluster-name-cell {
          max-width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        // 表格操作列样式
        .action-column {
          width: 50px;
          min-width: 50px;
          max-width: 50px;
        }
      }
      
      .associate-tip {
        margin-left: 12px;
        color: #ff7d00;
        font-size: 12px;
        display: inline-block;
        vertical-align: middle;
      }
      
      .service-source-container {
        display: flex;
        align-items: center;
      }
      
      .cluster-table-container {
        width: 100%;
        
        .cluster-table-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          span {
            font-weight: 500;
          }
        }
        
        .cluster-table {
          width: 100%;
          border: 1px solid #E9ECF0;
          border-radius: 2px;
          
          :global {
            .acud-table-pagination {
              margin-right: 16px;
            }
            
            .acud-table-thead > tr > th {
              background-color: #F2F3F5;
              color: #182334;
              font-weight: 500;
            }
            
            .acud-radio-wrapper {
              margin-right: 0;
            }
            
            .acud-table-row {
              &:hover {
                cursor: pointer;
              }
            }
            
            .acud-empty-image {
              height: 60px;
            }
            
            .acud-table-cell {
              padding: 12px 16px;
            }
          }
        }
      }
      
      // 集群选择表格样式增强
      :global {
        .acud-table-wrapper {
          border: 1px solid #E9ECF0; 
          border-radius: 4px;
          overflow: hidden; // 确保圆角边框效果
          
          .acud-table {
            margin: 0; // 移除表格默认边距
          }
          
          .acud-table-row.acud-table-row-selected > td {
            background-color: #f0f5ff;
          }
        }
      }
    }
  }
  
  // 底部操作区
  .create-instance-footer {
    background-color: #fff;
    bottom: 0;
    box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    display: flex;
    left: 180px; /* 左侧导航栏宽度 */
    padding: 16px 24px;
    position: fixed;
    right: 0;
    width: auto; /* 不再设置固定宽度 */
    z-index: 10;
    
    .create-instance-footer-content {
      display: flex;
      justify-content: flex-start;
      width: 100%;
      z-index: 1;
      
      button + button {
        margin-left: 8px;
      }
    }
  }
}
