import React, { useState, useEffect, useRef } from 'react';
import { Button, Table, Space, Modal, Form, Input, Radio, toast, Empty, Search, Switch, Tabs, Tooltip } from 'acud';
import { OutlinedPlusNew } from 'acud-icon';
import { useRequest } from 'ahooks';
import { ColumnsType } from 'acud/lib/table';

import { 
  getIpRestrictionList, 
  createIpRestriction, 
  updateIpRestriction,
  deleteIpRestriction,
  IpRestrictionType 
} from '@/apis/ipRestriction';
import styles from './index.module.less';

interface IpBlackWhiteListProps {
  instanceId: string;
  detail?: any;
}

const { TextArea } = Input;
const { TabPane } = Tabs;

const IpBlackWhiteList: React.FC<IpBlackWhiteListProps> = ({ instanceId, detail }) => {
  const [searchValue, setSearchValue] = useState<string>('');
  const [ipRestrictionList, setIpRestrictionList] = useState<IpRestrictionType[]>([]);
  
  // 创建弹窗相关状态
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [createForm] = Form.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);
  
  // 编辑弹窗相关状态
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [editForm] = Form.useForm();
  const [editSubmitting, setEditSubmitting] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<IpRestrictionType | null>(null);

  // 规则类型限制状态
  const [hasWhitelist, setHasWhitelist] = useState<boolean>(false);
  const [hasBlacklist, setHasBlacklist] = useState<boolean>(false);

  console.log('IpBlackWhiteList组件接收的instanceId:', instanceId);

  // 获取IP黑白名单列表
  const { loading, run: fetchIpRestrictionList } = useRequest(
    () => {
      // 使用实例的地域信息
      const instanceRegion = detail?.data?.region;
      console.log('获取IP黑白名单列表，实例ID:', instanceId, '实例地域:', instanceRegion);
      return getIpRestrictionList(instanceId, searchValue ? { name: searchValue } : undefined, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        console.log('获取IP黑白名单列表成功:', res);
        // 无论是否有结果，都要设置列表数据
        const resultList = res?.result || [];
        setIpRestrictionList(resultList);
        
        // 分析现有规则类型，设置限制状态
        const whitelistExists = resultList.some((rule: IpRestrictionType) => rule.type === 'whitelist');
        const blacklistExists = resultList.some((rule: IpRestrictionType) => rule.type === 'blacklist');
        setHasWhitelist(whitelistExists);
        setHasBlacklist(blacklistExists);
        
        console.log('规则类型分析 - 已有白名单:', whitelistExists, '已有黑名单:', blacklistExists);
      },
      onError: (error) => {
        console.error('获取IP黑白名单列表失败:', error);
        toast.error({
          message: '获取IP黑白名单列表失败',
          duration: 3
        });
      }
    }
  );

  // 验证IP地址/地址段是否合法
  const validateIpAddresses = (_: any, value: string) => {

    const ipLines = value.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    for (const ip of ipLines) {
      // 验证IP地址格式 (IPv4)
      if (ip.includes('/')) {
        // CIDR格式 (IP地址段) 如 ***********/24
        const [ipPart, prefixPart] = ip.split('/');
        const ipv4Regex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        
        if (!ipv4Regex.test(ipPart)) {
          return Promise.reject(new Error('IP地址/IP地址段不合法'));
        }
        
        const prefix = parseInt(prefixPart, 10);
        if (isNaN(prefix) || prefix < 0 || prefix > 32) {
          return Promise.reject(new Error('IP地址/IP地址段不合法'));
        }
      } else {
        // 单个IP地址
        const ipv4Regex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        
        if (!ipv4Regex.test(ip)) {
          return Promise.reject(new Error('IP地址/IP地址段不合法'));
        }
      }
    }
    
    return Promise.resolve();
  };

  // 当instanceId变化时，获取列表
  useEffect(() => {
    if (instanceId) {
      fetchIpRestrictionList();
    }
  }, [instanceId]);

  // 搜索功能
  const handleSearch = (value: string) => {
    setSearchValue(value);
    if (instanceId) {
      fetchIpRestrictionList();
    }
  };

  // 表格列定义
  const columns: ColumnsType<IpRestrictionType> = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: '启用状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 181,
      render: (enabled: boolean) => (
        <span className={styles.status}>
          <span className={`${styles.circle} ${enabled ? styles.statusSuccess : styles.statusInactive}`}></span>
          <span className={styles.statusText}>{enabled ? '已启用' : '已禁用'}</span>
        </span>
      )
    },
    {
      title: '规则类型',
      dataIndex: 'type',
      key: 'type',
      width: 181,
      render: (type: string) => (
        <span>{type === 'whitelist' ? '白名单' : '黑名单'}</span>
      )
    },
    {
      title: '生效粒度',
      dataIndex: 'scope',
      key: 'scope',
      render: () => '网关全局'
    },
    {
      title: 'IP地址/地址段',
      dataIndex: 'ipAddresses',
      key: 'ipAddresses',
      render: (ipAddresses: string[]) => (
        <div style={{ maxWidth: 300 }}>
          {ipAddresses?.map((ip, index) => (
            <div key={index}>{ip}</div>
          )) || '-'}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => handleEdit(record)}>编辑</a>
          <a onClick={(e: React.MouseEvent<HTMLElement>) => {
            Modal.confirm({
              title: '确定要删除当前IP黑白名单规则吗？',
              content: (
                <div>
                  规则删除后无法恢复，请谨慎操作！请确定是否要删除规则"{record.name}"？
                </div>
              ),
              okText: '确定',
              cancelText: '取消',
              width: 400,
              onOk: () => {
                if (record.id) {
                  return handleDelete(record.id);
                }
                return Promise.resolve();
              }
            });
          }}>删除</a>
        </Space>
      )
    }
  ];

  // 检查是否可以创建新规则
  const canCreateRule = () => {
    // 如果已有黑白名单规则各一条，则不能创建
    return !(hasWhitelist && hasBlacklist);
  };

  // 获取默认规则类型
  const getDefaultRuleType = () => {
    if (hasWhitelist && !hasBlacklist) {
      return 'blacklist'; // 已有白名单，默认选择黑名单
    } else if (hasBlacklist && !hasWhitelist) {
      return 'whitelist'; // 已有黑名单，默认选择白名单
    }
    return 'whitelist'; // 都没有或都有，默认选择白名单
  };

  // 检查创建时是否禁用规则类型选项
  const isCreateRuleTypeDisabled = (type: 'whitelist' | 'blacklist') => {
    // 如果已有黑白名单规则各一条，则禁用创建按钮，但这里不应该禁用选项
    if (hasWhitelist && hasBlacklist) {
      return false; // 在弹窗中应该允许选择
    }
    // 如果已有该类型的规则，则禁用该选项
    return type === 'whitelist' ? hasWhitelist : hasBlacklist;
  };

  // 检查编辑时是否禁用规则类型选项
  const isEditRuleTypeDisabled = (type: 'whitelist' | 'blacklist') => {
    if (!currentRecord) return false;
    
    // 如果当前记录就是该类型，则可以选择（不禁用）
    if (currentRecord.type === type) {
      return false;
    }
    
    // 如果已有该类型的其他规则，则禁用
    return type === 'whitelist' ? hasWhitelist : hasBlacklist;
  };

  // 打开创建弹窗
  const handleCreate = () => {
    console.log('打开创建IP黑白名单规则弹窗');
    createForm.resetFields();
    
    // 确定可用的规则类型并设置为默认值
    let defaultType: 'whitelist' | 'blacklist' = 'whitelist';
    
    if (hasWhitelist && !hasBlacklist) {
      defaultType = 'blacklist'; // 已有白名单，只能选黑名单
    } else if (hasBlacklist && !hasWhitelist) {
      defaultType = 'whitelist'; // 已有黑名单，只能选白名单
    } else if (!hasWhitelist && !hasBlacklist) {
      defaultType = 'whitelist'; // 都没有，默认选白名单
    }
    
    console.log('设置默认规则类型:', defaultType);
    
    createForm.setFieldsValue({ 
      type: defaultType,
      scope: 'global',
      enabled: false 
    });
    setCreateModalVisible(true);
  };

  // 处理创建提交
  const handleCreateSubmit = async () => {
    try {
      setSubmitting(true);
      const values = await createForm.validateFields();
      console.log('创建IP黑白名单规则表单数据:', values);
      
      // 处理IP地址数据
      const ipAddressesArray = values.ipAddresses
        .split('\n')
        .map((ip: string) => ip.trim())
        .filter((ip: string) => ip.length > 0);

      const createData = {
        enabled: values.enabled,
        name: values.name,
        description: values.description || '',
        type: values.type,
        scope: values.scope || 'global',
        ipAddresses: ipAddressesArray
      };

      console.log('提交创建IP黑白名单规则，数据:', createData);

      // 使用实例的地域信息
      const instanceRegion = detail?.data?.region;
      await createIpRestriction(instanceId, createData, instanceRegion);
      
      toast.success({
        message: 'IP黑白名单规则创建成功',
        duration: 3
      });
      
      setCreateModalVisible(false);
      createForm.resetFields();
      fetchIpRestrictionList(); // 重新获取列表以更新规则类型限制状态
    } catch (error) {
      console.error('创建IP黑白名单规则失败:', error);
      toast.error({
        message: '创建IP黑白名单规则失败',
        duration: 3
      });
    } finally {
      setSubmitting(false);
    }
  };

  // 打开编辑弹窗
  const handleEdit = (record: IpRestrictionType) => {
    console.log('打开编辑IP黑白名单规则弹窗，记录:', record);
    setCurrentRecord(record);
    editForm.setFieldsValue({
      enabled: record.enabled,
      name: record.name,
      description: record.description || '',
      type: record.type,
      scope: 'global',
      ipAddresses: record.ipAddresses?.join('\n') || ''
    });
    setEditModalVisible(true);
  };

  // 处理编辑提交
  const handleEditSubmit = async () => {
    try {
      setEditSubmitting(true);
      const values = await editForm.validateFields();
      console.log('编辑IP黑白名单规则表单数据:', values);
      
      // 处理IP地址数据
      const ipAddressesArray = values.ipAddresses
        .split('\n')
        .map((ip: string) => ip.trim())
        .filter((ip: string) => ip.length > 0);

      const updateData = {
        enabled: values.enabled,
        name: values.name,
        description: values.description || '',
        type: values.type,
        scope: values.scope || 'global',
        ipAddresses: ipAddressesArray
      };

      console.log('提交更新IP黑白名单规则，数据:', updateData);

      // 使用实例的地域信息
      const instanceRegion = detail?.data?.region;
      await updateIpRestriction(instanceId, currentRecord!.id!, updateData, instanceRegion);
      
      toast.success({
        message: 'IP黑白名单规则更新成功',
        duration: 3
      });
      
      setEditModalVisible(false);
      editForm.resetFields();
      setCurrentRecord(null);
      fetchIpRestrictionList(); // 重新获取列表以更新规则类型限制状态
    } catch (error) {
      console.error('更新IP黑白名单规则失败:', error);
      toast.error({
        message: '更新IP黑白名单规则失败',
        duration: 3
      });
    } finally {
      setEditSubmitting(false);
    }
  };

  // 删除规则
  const handleDelete = async (id: string): Promise<void> => {
    try {
      // 使用实例的地域信息
      const instanceRegion = detail?.data?.region;
      console.log('删除IP黑白名单规则，ID:', id, '实例地域:', instanceRegion);
      await deleteIpRestriction(instanceId, id, instanceRegion);
      
      toast.success({
        message: 'IP黑白名单规则删除成功',
        duration: 3
      });
      
      // 手动更新本地状态，确保即使API返回空结果也能正确更新UI
      const updatedList = ipRestrictionList.filter(item => item.id !== id);
      setIpRestrictionList(updatedList);
      
      // 更新规则类型状态
      const whitelistExists = updatedList.some(rule => rule.type === 'whitelist');
      const blacklistExists = updatedList.some(rule => rule.type === 'blacklist');
      setHasWhitelist(whitelistExists);
      setHasBlacklist(blacklistExists);
      
      // 然后再重新获取列表以确保数据同步
      fetchIpRestrictionList();
    } catch (error) {
      console.error('删除IP黑白名单规则失败:', error);
      toast.error({
        message: '删除IP黑白名单规则失败',
        duration: 3
      });
      throw error; // 重新抛出错误以便Modal.confirm能够处理
    }
  };

  // 刷新列表
  const handleRefresh = () => {
    console.log('刷新IP黑白名单列表');
    fetchIpRestrictionList();
  };

  return (
    <div className={styles.container}>
      {/* 标签页 */}
      <Tabs activeKey="ip-blackwhite-list" className={styles.tabs}>
        <TabPane tab="IP 黑白名单" key="ip-blackwhite-list">
          {/* 操作栏 */}
          <div className={styles.toolbar}>
            <Tooltip 
              title={!canCreateRule() ? "当前实例已有网关全局的黑白名单规则" : ""}
              placement="top"
            >
              <Button 
                type="primary" 
                icon={<OutlinedPlusNew />}
                onClick={handleCreate}
                disabled={!canCreateRule()}
              >
                创建黑白名单规则
              </Button>
            </Tooltip>
            <Search
              placeholder="请输入黑白名单规则名称"
              allowClear
              enterButton={false}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              style={{ width: 240 }}
            />
          </div>

          {/* 表格 */}
          <Table<IpRestrictionType>
            dataSource={ipRestrictionList}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            locale={{   emptyText: (
              <Empty 
                description={
                  <span>
                    暂无 IP 黑白名单规则。
                    <a onClick={handleCreate} style={{ marginLeft: '4px', cursor: 'pointer' }}>
                      <OutlinedPlusNew style={{ marginRight: '4px' }} />
                      创建 IP 黑白名单
                    </a>
                  </span>
                } 
              />
            )  }}
            className={styles.table}
          />
        </TabPane>
      </Tabs>

      {/* 创建弹窗 */}
      <Modal
        title="创建黑白名单规则"
        visible={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        width={600}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button
              onClick={() => {
                setCreateModalVisible(false);
                createForm.resetFields();
              }}
              style={{ marginRight: 8 }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleCreateSubmit}
              loading={submitting}
            >
              确定
            </Button>
          </div>
        }
      >
        <Form
          form={createForm}
          layout="horizontal"
          labelWidth={80}
          labelAlign="left"
        >
          <Form.Item
            label="规则名称"
            name="name"
            extra={
              <span style={{ whiteSpace: 'nowrap' }}>
                支持大小写字母、数字以及-_特殊字符，长度限制在 2-64 之间
              </span>
            }
            rules={[
              { required: true, message: '请输入规则名称' },
              { min: 2, max: 64, message: '规则名称长度为2-64个字符' },
              { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线(_)和连字符(-)' }
            ]}
          >
            <Input placeholder="请输入" maxLength={64} style={{ width: 400 }} />
          </Form.Item>

          <Form.Item
            label="规则类型"
            name="type"
            rules={[{ required: true, message: '请选择规则类型' }]}
          >
            <Radio.Group className={styles.ruleTypeGroup}>
              
              <Radio.Button 
                value="whitelist" 
                disabled={isCreateRuleTypeDisabled('whitelist')}
              >
                白名单
              </Radio.Button>
              <Radio.Button 
                value="blacklist" 
                disabled={isCreateRuleTypeDisabled('blacklist')}
              >
                黑名单
              </Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="生效粒度"
            name="scope"
            initialValue="global"
          >
            <Radio.Group>
              <Radio.Button value="global">网关全局</Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="IP地址/地址段"
            name="ipAddresses"
            rules={[
              { required: true, message: '请输入IP地址或地址段' },
              { validator: validateIpAddresses }
            ]}
          >
            <TextArea
              autoSize={{ minRows: 4, maxRows: 8 }}
              placeholder="地址段配置，每个条目一行，以回车分隔"
              style={{ width: 400 }}
            />
          </Form.Item>

          <Form.Item
            label="启用"
            name="enabled"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑黑白名单规则"
        visible={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
          setCurrentRecord(null);
        }}
        width={600}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button
              onClick={() => {
                setEditModalVisible(false);
                editForm.resetFields();
                setCurrentRecord(null);
              }}
              style={{ marginRight: 8 }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleEditSubmit}
              loading={editSubmitting}
            >
              确定
            </Button>
          </div>
        }
      >
        <Form
          form={editForm}
          layout="horizontal"
          labelWidth={80}
          labelAlign="left"
        >
          <Form.Item
            label="规则名称"
            name="name"
            extra={
              <span style={{ whiteSpace: 'nowrap' }}>
                支持大小写字母、数字以及-_特殊字符，长度限制在 2-64 之间
              </span>
            }
            rules={[
              { required: true, message: '请输入规则名称' },
              { min: 2, max: 64, message: '规则名称长度为2-64个字符' },
              { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线(_)和连字符(-)' }
            ]}
          >
            <Input placeholder="请输入" maxLength={64} style={{ width: 400 }} />
          </Form.Item>

          <Form.Item
            label="规则类型"
            name="type"
            rules={[{ required: true, message: '请选择规则类型' }]}
          >
            <Radio.Group className={styles.ruleTypeGroup}>
              
            {/* <Radio.Button 
                value="whitelist" 
                disabled={isEditRuleTypeDisabled('whitelist')}
              >
                白名单
              </Radio.Button>
              <Radio.Button 
                value="blacklist" 
                disabled={isEditRuleTypeDisabled('blacklist')}
              >
                黑名单
              </Radio.Button> */}

                <Radio.Button 
                  value="whitelist" 
                  disabled={isEditRuleTypeDisabled('whitelist')}
                  className={isEditRuleTypeDisabled('whitelist') ? styles.disabledButton : ''}
                >
                  白名单
                <Tooltip 
                title={isEditRuleTypeDisabled('whitelist') ? "当前实例已有一条网关全局的白名单规则" : ""}
                placement="top">
                </Tooltip>
                </Radio.Button>
              

                <Radio.Button 
                  value="blacklist" 
                  disabled={isEditRuleTypeDisabled('blacklist')}
                  className={isEditRuleTypeDisabled('blacklist') ? styles.disabledButton : ''}
                >
                  黑名单
                <Tooltip 
                title={isEditRuleTypeDisabled('blacklist') ? "当前实例已有一条网关全局的黑名单规则" : ""}
                placement="top">
                </Tooltip>
                </Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="生效粒度"
            name="scope"
            initialValue="global"
          >
            <Radio.Group>
              <Radio.Button value="global">网关全局</Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="IP地址/地址段"
            name="ipAddresses"
            rules={[
              { required: true, message: '请输入IP地址或地址段' },
              { validator: validateIpAddresses }
            ]}
          >
            <TextArea
              autoSize={{ minRows: 4, maxRows: 8 }}
              placeholder="地址段配置，每个条目一行，以回车分隔"
              style={{ width: 400 }}
            />
          </Form.Item>

          <Form.Item
            label="启用"
            name="enabled"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default IpBlackWhiteList; 