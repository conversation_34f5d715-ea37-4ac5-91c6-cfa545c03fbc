.container {
  padding: 0;
  background: #ffffff;
}

.status {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.statusText {
  color: #151b26;
  font-size: 12px;
  line-height: 20px;
}

.circle {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  background: #d9d9d9;
}

.statusSuccess {
  background: #30bf13;
}

.statusInactive {
  background: #bfbfbf;
}

.ruleTypeContainer {
  position: relative;
}

.ruleTypeGroup {
  :global(.acud-radio-button-wrapper) {
    
    &:first-child {
      border-radius: 4px 0 0 4px;
      border-right: none;
    }
    
    &:last-child {
      border-radius: 0 4px 4px 0;
      border-left: none;
    }
    
    &.acud-radio-button-wrapper-checked {
      background: #e6f0ff;
      border-color: #2468f2;
      color: #2468f2;
    }
    
    &.acud-radio-button-wrapper-disabled {
      background: #f7f7f9;
      border-color: #d4d6d9;
      color: #b8babf;
      cursor: not-allowed;
      
      &:hover {
        background: #f7f7f9;
        border-color: #d4d6d9;
        color: #b8babf;
      }
    }
  }
}

.disabledButton {
  :global(.acud-radio-button-wrapper-disabled) {
    background: #f7f7f9 !important;
    border-color: #d4d6d9 !important;
    color: #b8babf !important;
    cursor: not-allowed !important;
  }
}

.tabs {
  :global(.acud-tabs-nav) {
    margin-bottom: 18px;
  }
  
  :global(.acud-tabs-content) {
    padding: 0;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  gap: 640px;
}




