import React, { useState, useEffect, useRef } from 'react';
import { Button, Table, Space, Tabs, Popconfirm, toast, Empty, Modal, Drawer, Form, Input, Radio, Transfer, Alert, Loading, Tooltip, Tag, Slider, InputNumber, Progress } from 'acud';
import { OutlinedRefresh, OutlinedCopy, OutlinedCheckCircle, OutlinedInfoCircle, OutlinedPlusNew } from 'acud-icon';
import { useRequest } from 'ahooks';

import { getConsumerList, deleteConsumer, updateConsumer, ConsumerType, PageResponseType, createConsumer, getConsumerDetail } from '@/apis/consumer';
import { getRouteList, RouteType, getRouteDetail, RouteDetailType } from '@/apis/route';
import styles from './index.module.less';

interface ConsumerListProps {
  detail: any;
  instanceId: string;
  requestDetail?: () => void;
}

// 定义API响应类型
interface ApiResponse {
  success: boolean;
  status: number;
  page?: {
    orderBy: string;
    order: string;
    pageNo: number;
    pageSize: number;
    totalCount: number;
    result: ConsumerType[];
  };
}

// 定义消费者详情接口类型
interface ConsumerDetailType extends ConsumerType {
  authInfo?: {
    token: string;
  };
  routes?: Array<{
    routeId: string;
    routeName: string;
    createTime: string;
    authEnabled: boolean;
  }>;
  quotaValue?: number; // 剩余配额值
}

const { TabPane } = Tabs;

const ConsumerList: React.FC<ConsumerListProps> = (props) => {
  const { instanceId } = props;
  const [activeTab, setActiveTab] = useState<string>('list');
  const [pageParams, setPageParams] = useState({
    pageNo: 1,
    pageSize: 10,
    orderBy: 'createTime',
    order: 'desc'
  });
  const [totalCount, setTotalCount] = useState<number>(0);
  const [consumerList, setConsumerList] = useState<ConsumerType[]>([]);

  // 创建消费者抽屉相关状态
  const [createDrawerVisible, setCreateDrawerVisible] = useState<boolean>(false);
  const [createForm] = Form.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [routeList, setRouteList] = useState<RouteType[]>([]);
  const [selectedRouteIds, setSelectedRouteIds] = useState<string[]>([]);
  // 添加状态跟踪消费者名称验证状态
  const [nameValidating, setNameValidating] = useState<boolean>(false);
  const [nameExists, setNameExists] = useState<boolean>(false);
  // 实时跟踪输入的消费者名称
  const [consumerNameInput, setConsumerNameInput] = useState<string>('');
  // 用于标记并发校验的请求 id，以忽略过时结果
  const validateRequestIdRef = useRef(0);
  // 限额配置相关状态
  const [quotaType, setQuotaType] = useState<'unlimited' | 'custom'>('unlimited');
  const [totalQuota, setTotalQuota] = useState<number>(500000); // 默认50万token

  // 编辑消费者抽屉相关状态
  const [editDrawerVisible, setEditDrawerVisible] = useState<boolean>(false);
  const [editForm] = Form.useForm();
  const [editSubmitting, setEditSubmitting] = useState<boolean>(false);
  const [currentConsumer, setCurrentConsumer] = useState<ConsumerDetailType | null>(null);
  const [editSelectedRouteIds, setEditSelectedRouteIds] = useState<string[]>([]);
  const [consumerDetailLoading, setConsumerDetailLoading] = useState<boolean>(false);
  // 添加编辑消费者限额配置相关状态
  const [editQuotaType, setEditQuotaType] = useState<'unlimited' | 'custom'>('unlimited');
  const [editTotalQuota, setEditTotalQuota] = useState<number>(500000); // 默认50万token

  // 添加消费者详情抽屉相关状态
  const [detailDrawerVisible, setDetailDrawerVisible] = useState<boolean>(false);
  const [consumerDetail, setConsumerDetail] = useState<ConsumerDetailType | null>(null);
  const [detailLoading, setDetailLoading] = useState<boolean>(false);

  // 路由认证状态映射
  const [routeAuthMap, setRouteAuthMap] = useState<Record<string, boolean>>({});

  // 检查instanceId
  useEffect(() => {
    console.log('ConsumerList组件接收的instanceId:', instanceId);
  }, [instanceId]);

  // 获取消费者列表
  const { loading, run: fetchConsumerList } = useRequest<PageResponseType<ConsumerType>, any>(
    () => {
      console.log('获取消费者列表，实例ID:', instanceId);
      // 使用实例的地域信息
      const instanceRegion = props.detail?.data?.region;
      return getConsumerList(instanceId, pageParams, instanceRegion);
    },
    {
      refreshDeps: [instanceId, pageParams],
      manual: true,
      onSuccess: (res) => {
        console.log('获取消费者列表成功:', res);
        if (res?.page) {
          setTotalCount(res.page.totalCount);
          setConsumerList(res.page.result || []);
        }
      },
      onError: (error) => {
        console.error('获取消费者列表失败:', error);
        toast.error({
          message: '获取消费者列表失败',
          duration: 3
        });
      }
    }
  );

  // 获取路由列表
  const { loading: routeLoading, run: fetchRouteList } = useRequest(
    () => {
      console.log('获取路由列表，实例ID:', instanceId);
      // 使用实例的地域信息
      const instanceRegion = props.detail?.data?.region;
      return getRouteList(instanceId, {
        pageNo: 1,
        pageSize: 100, // 一次获取较多数据以展示在穿梭框中
        orderBy: 'createTime',
        order: 'desc'
      }, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        console.log('获取路由列表成功:', res);
        if (res?.page?.result) {
          setRouteList(res.page.result);
          // 并发查询每条路由的认证状态
          // 使用实例的地域信息
          const instanceRegion = props.detail?.data?.region;
          Promise.all(
            res.page.result.map(route =>
              getRouteDetail(instanceId, route.routeName, instanceRegion)
                .then(r => ({ name: route.routeName, authEnabled: r.result?.authEnabled }))
                .catch(() => ({ name: route.routeName, authEnabled: true }))
            )
          ).then(list => {
            const map: Record<string, boolean> = {};
            list.forEach(item => { map[item.name] = item.authEnabled; });
            setRouteAuthMap(map);
          });
        }
      },
      onError: (error) => {
        console.error('获取路由列表失败:', error);
        toast.error({
          message: '获取路由列表失败',
          duration: 5
        });
      }
    }
  );

  // 当instanceId变化时，获取列表
  useEffect(() => {
    if (instanceId) {
      fetchConsumerList();
    }
  }, [instanceId]);

  // 添加新的useEffect，用于监听pageParams变化并重新获取数据
  useEffect(() => {
    if (instanceId) {
      fetchConsumerList();
    }
  }, [pageParams]);

  // 在用户输入时动态验证消费者名称（防抖100ms）
  useEffect(() => {
    if (consumerNameInput) {
      const handler = setTimeout(() => {
        validateConsumerName(consumerNameInput);
      }, 100);
      return () => clearTimeout(handler);
    } else {
      // 输入为空时，重置校验状态并清除表单错误
      setNameExists(false);
      createForm.setFields([{ name: 'consumerName', errors: [] }]);
    }
  }, [consumerNameInput]);

  // 表格列定义
  const columns = [
    {
      title: '消费者名称/ID',
      key: 'consumerName',
      width: 340,
      render: (_, record: ConsumerType) => (
        <div>
          <a style={{ cursor: 'pointer', color: '#2468f2' }} onClick={() => handleShowDetail(record)}>
            {record.consumerName}
          </a>
          <div style={{ fontSize: '12px', color: '#303540' }}>{record.consumerId}
            {record.srcProduct === 'pom' && (
              <Tag color="active" style={{ marginLeft: 10 }}>百舸推理</Tag>
            )}
          </div>
        </div>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 300,
      render: (text: string) => (
        <Tooltip title={text || '-'}>
          <span
            style={{
              display: 'inline-block',
              maxWidth: 280, // 比列宽略小，留出padding
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              verticalAlign: 'middle',
            }}
          >
            {text || '-'}
          </span>
        </Tooltip>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: ConsumerType) => {
        const isPomProduct = record.srcProduct === 'pom';

        return (
          <Space size="middle">
            <a
              onClick={() => !isPomProduct && handleEdit(record)}
              style={{
                color: isPomProduct ? '#ccc' : undefined,
                cursor: isPomProduct ? 'not-allowed' : 'pointer'
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                if (isPomProduct) return;
                Modal.confirm({
                  title: '确定要删除当前消费者吗？',
                  content: (
                    <div>
                      消费者删除后无法恢复，请谨慎操作！请确定是否要删除消费者"{record.consumerName}"？
                    </div>
                  ),
                  okText: '确定',
                  cancelText: '取消',
                  width: 400,
                  onOk: () => {
                    if (record.consumerId) {
                      return handleDelete(record.consumerId);
                    }
                    return Promise.resolve();
                  }
                });
              }}
              style={{
                color: isPomProduct ? '#ccc' : undefined,
                cursor: isPomProduct ? 'not-allowed' : 'pointer'
              }}
            >
              删除
            </a>
          </Space>
        );
      }
    }
  ];

  // 验证消费者名称是否已存在
  const validateConsumerName = async (name: string) => {
    // 全等判断
    if (!name) {
      setNameExists(false);
      return true;
    }
    setNameValidating(true);
    // 使用实例的地域信息
    const instanceRegion = props.detail?.data?.region;
    const res = await getConsumerList(instanceId, { pageNo:1, pageSize:200, orderBy:'createTime', order:'desc' }, instanceRegion);
    setNameValidating(false);
//直接调用 res.page.result.some()，当 result 为 null 或 undefined 时就会报错，在这里添加多层空值校验
    if (res?.success && res.page?.result && Array.isArray(res.page.result)) {
      const exists = res.page.result.some(c => c.consumerName === name);
      setNameExists(exists);
      return !exists;
    }
    return true;
  };

  // 处理消费者名称输入框失焦事件
  const handleConsumerNameBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value) {
      validateConsumerName(value);
    } else {
      setNameExists(false);
    }
  };

  // 创建消费者
  const handleCreate = () => {
    createForm.resetFields();
    setConsumerNameInput('');
    setNameExists(false);
    setQuotaType('unlimited');
    setTotalQuota(500000); // 默认50万token
    setSelectedRouteIds([]);
    // 设置表单初始值
    createForm.setFieldsValue({ 
      quotaType: 'unlimited',
      totalQuota: 50 // 50万，表单显示为50（万）
    });
    fetchRouteList();
    setCreateDrawerVisible(true);
  };

  // 穿梭框数据源
  const getTransferDataSource = () => {
    return routeList.map(route => ({
      key: route.routeName,
      title: route.routeName,
      description: `${route.matchPath.matchType}: ${route.matchPath.value}`,
      // 如果未查询到状态，默认认为已认证
      authEnabled: routeAuthMap[route.routeName] ?? true
    }));
  };

  // 创建消费者提交
  const handleCreateSubmit = async () => {
    try {
      // 先验证表单字段
      const values = await createForm.validateFields();
      console.log('创建消费者表单数据:', values, '选中的路由IDs:', selectedRouteIds);

      // 再次验证消费者名称是否存在
      const isNameValid = await validateConsumerName(values.consumerName);
      if (!isNameValid) {
        return; // 如果名称已存在，阻止提交
      }

      // 验证限额配置
      if (values.quotaType === 'custom' && (values.totalQuota === null || values.totalQuota === undefined || values.totalQuota < 0)) {
        toast.error({
          message: 'Token 配额不能少于0',
          duration: 3
        });
        return;
      }

      setSubmitting(true);

      // 构建创建消费者请求参数
      const createData: ConsumerType = {
        consumerName: values.consumerName,
        description: values.description,
        authType: values.authType,
        routeNames: selectedRouteIds,
        unlimitedQuota: values.quotaType === 'unlimited',
        totalQuota: values.quotaType === 'custom' ? values.totalQuota * 10000 : undefined
      };

      console.log('创建消费者请求数据:', createData);

      // 调用创建消费者接口
      // 使用实例的地域信息
      const instanceRegion = props.detail?.data?.region;
      const res = await createConsumer({ instanceId }, createData, instanceRegion);
      console.log('创建消费者响应:', res);

      if (res?.success) {
        toast.success({
          message: '消费者创建成功',
          duration: 5
        });
        setCreateDrawerVisible(false);
        fetchConsumerList(); // 刷新列表
      } else {
        // 处理错误消息是对象的情况
        let errorMsg = '创建消费者失败';
        const errorRes = res as any; // 使用类型断言

        if (errorRes.message) {
          if (typeof errorRes.message === 'string') {
            errorMsg = errorRes.message;
          } else if (typeof errorRes.message === 'object') {
            // 如果message是对象，尝试获取global字段或第一个可用的错误消息
            errorMsg = errorRes.message.global || Object.values(errorRes.message)[0] || errorMsg;
          }
        }

        toast.error({
          message: errorMsg,
          duration: 5
        });
      }
    } catch (error) {
      console.error('创建消费者失败:', error);
      toast.error({
        message: '表单校验失败或创建过程中出现错误',
        duration: 5
      });
    } finally {
      setSubmitting(false);
    }
  };

  // 获取消费者详情 (用于编辑抽屉)
  const fetchConsumerDetail = async (consumerId: string) => {
    try {
      setConsumerDetailLoading(true);
      // 使用实例的地域信息
      const instanceRegion = props.detail?.data?.region;
      console.log('获取消费者详情, instanceId:', instanceId, 'consumerId:', consumerId, '实例地域:', instanceRegion);
      const res = await getConsumerDetail({
        instanceId,
        consumerId
      }, instanceRegion);
      console.log('获取消费者详情响应:', res);

      if (res?.success && res.result) {
        // 设置当前编辑的消费者信息
        setCurrentConsumer(res.result as ConsumerDetailType);

        // 处理限额配置数据
        const quotaType = res.result.unlimitedQuota ? 'unlimited' : 'custom';
        const totalQuota = res.result.unlimitedQuota ? 500000 : (res.result.totalQuota ?? 500000);
        
        setEditQuotaType(quotaType);
        setEditTotalQuota(totalQuota);

        // 初始化表单值
        editForm.setFieldsValue({
          consumerName: res.result.consumerName,
          description: res.result.description || '',
          authType: res.result.authType,
          quotaType: quotaType,
          totalQuota: Math.max(0, Math.floor(totalQuota / 10000)) // 转换为万单位，最小为0
        });

        // 从详情中提取已授权路由名称（注意：API返回的是routeName而不是routeId）
        const routeNames = (res.result as ConsumerDetailType).routes?.map(route => route.routeName) || [];
        setEditSelectedRouteIds(routeNames);

        // 获取路由列表用于穿梭框展示
        fetchRouteList();

        return res.result;
      } else {
        toast.error({
          message: '获取消费者详情失败',
          duration: 3
        });
        return null;
      }
    } catch (error) {
      console.error('获取消费者详情失败:', error);
      toast.error({
        message: '获取消费者详情失败',
        duration: 3
      });
      return null;
    } finally {
      setConsumerDetailLoading(false);
    }
  };

  // 打开消费者详情抽屉
  const handleShowDetail = async (record: ConsumerType) => {
    if (!record.consumerId) {
      console.error('查看消费者详情失败: 缺少consumerId');
      return;
    }

    console.log('查看消费者详情:', record);
    setDetailDrawerVisible(true);

    // 获取消费者详情
    await fetchConsumerDetailForDrawer(record.consumerId);
  };

  // 编辑消费者
  const handleEdit = async (record: ConsumerType) => {
    if (!record.consumerId) {
      console.error('编辑消费者失败: 缺少consumerId');
      return;
    }

    console.log('编辑消费者:', record);
    setEditDrawerVisible(true);

    // 获取消费者详情
    await fetchConsumerDetail(record.consumerId);
  };

  // 处理编辑穿梭框路由选择变化
  const handleEditRouteChange = (nextTargetKeys: string[]) => {
    console.log('编辑消费者 - 选择的路由变更:', nextTargetKeys);
    setEditSelectedRouteIds(nextTargetKeys);
  };

  // 提交编辑消费者
  const handleEditSubmit = async () => {
    if (!currentConsumer?.consumerId) {
      console.error('提交编辑失败: 缺少consumerId');
      return;
    }

    try {
      const values = await editForm.validateFields();
      console.log('编辑消费者表单数据:', values, '选中的路由IDs:', editSelectedRouteIds);

      // 验证限额配置
      if (values.quotaType === 'custom' && (values.totalQuota === null || values.totalQuota === undefined || values.totalQuota < 0)) {
        toast.error({
          message: 'Token 配额不能少于0',
          duration: 3
        });
        return;
      }

      setEditSubmitting(true);

      // 构建编辑消费者请求参数
      const updateData = {
        description: values.description,
        routeNames: editSelectedRouteIds,
        unlimitedQuota: values.quotaType === 'unlimited',
        totalQuota: values.quotaType === 'custom' ? values.totalQuota * 10000 : undefined
      };

      console.log('更新消费者请求数据:', updateData);

      // 调用更新消费者接口
      // 使用实例的地域信息
      const instanceRegion = props.detail?.data?.region;
      const res = await updateConsumer({
        instanceId,
        consumerId: currentConsumer.consumerId
      }, updateData as ConsumerType, instanceRegion);

      console.log('编辑消费者响应:', res);

      if (res?.success) {
        setEditDrawerVisible(false);
        toast.success({
          message: `消费者 ${currentConsumer.consumerName} 更新成功`,
          duration: 5
        });
        fetchConsumerList(); // 刷新列表
      } else {
        // 处理错误消息是对象的情况
        let errorMsg = '更新消费者失败';
        const errorRes = res as any; // 使用类型断言

        if (errorRes.message) {
          if (typeof errorRes.message === 'string') {
            errorMsg = errorRes.message;
          } else if (typeof errorRes.message === 'object') {
            // 如果message是对象，尝试获取global字段或第一个可用的错误消息
            errorMsg = errorRes.message.global || Object.values(errorRes.message)[0] || errorMsg;
          }
        }

        toast.error({
          message: errorMsg,
          duration: 5
        });
      }
    } catch (error) {
      console.error('更新消费者失败:', error);
      toast.error({
        message: '表单校验失败或更新过程中出现错误',
        duration: 5
      });
    } finally {
      setEditSubmitting(false);
    }
  };

  // 复制认证token
  const copyAuthToken = () => {
    if (currentConsumer?.authInfo?.token) {
      navigator.clipboard.writeText(currentConsumer.authInfo.token)
        .then(() => {
          toast.success({
            message: '认证信息已复制到剪贴板',
            duration: 3
          });
        })
        .catch(err => {
          console.error('复制失败:', err);
          toast.error({
            message: '复制失败',
            duration: 3
          });
        });
    }
  };

  // 删除消费者
  const handleDelete = async (consumerId: string) => {
    try {
      // 使用实例的地域信息
      const instanceRegion = props.detail?.data?.region;
      console.log('删除消费者, instanceId:', instanceId, 'consumerId:', consumerId, '实例地域:', instanceRegion);
      const res = await deleteConsumer({
        instanceId,
        consumerId
      }, instanceRegion);
      console.log('删除消费者结果:', res);
      if (res?.success) {
        toast.success({
          message: '删除成功',
          duration: 3
        });
        fetchConsumerList();
      } else {
        toast.error({
          message: '删除失败',
          duration: 3
        });
      }
    } catch (error) {
      console.error('删除消费者失败:', error);
      toast.error({
        message: '删除消费者失败',
        duration: 3
      });
    }
  };

  // 刷新列表
  const handleRefresh = () => {
    console.log('刷新消费者列表');
    fetchConsumerList();
  };

  // 分页变化
  const handlePageChange = (pageNo: number, pageSize?: number) => {
    console.log('分页变化:', { pageNo, pageSize });
    setPageParams({
      ...pageParams,
      pageNo,
      pageSize: pageSize || pageParams.pageSize
    });
  };

  // 处理穿梭框路由选择变化
  const handleRouteChange = (nextTargetKeys: string[]) => {
    console.log('选择的路由变更:', nextTargetKeys);
    setSelectedRouteIds(nextTargetKeys);
  };

  // 获取消费者详情 (用于详情抽屉)
  const fetchConsumerDetailForDrawer = async (consumerId: string) => {
    try {
      setDetailLoading(true);
      // 使用实例的地域信息
      const instanceRegion = props.detail?.data?.region;
      console.log('获取消费者详情(详情抽屉), instanceId:', instanceId, 'consumerId:', consumerId, '实例地域:', instanceRegion);
      const res = await getConsumerDetail({
        instanceId,
        consumerId
      }, instanceRegion);
      console.log('获取消费者详情响应:', res);

      if (res?.success && res.result) {
        // 设置消费者详情信息
        setConsumerDetail(res.result as ConsumerDetailType);
        return res.result;
      } else {
        toast.error({
          message: '获取消费者详情失败',
          duration: 3
        });
        return null;
      }
    } catch (error) {
      console.error('获取消费者详情失败:', error);
      toast.error({
        message: '获取消费者详情失败',
        duration: 3
      });
      return null;
    } finally {
      setDetailLoading(false);
    }
  };

  // 复制认证token (用于详情抽屉)
  const copyDetailAuthToken = () => {
    if (consumerDetail?.authInfo?.token) {
      navigator.clipboard.writeText(consumerDetail.authInfo.token)
        .then(() => {
          toast.success({
            message: '认证信息已复制到剪贴板',
            duration: 3
          });
        })
        .catch(err => {
          console.error('复制失败:', err);
          toast.error({
            message: '复制失败',
            duration: 3
          });
        });
    }
  };

  return (
    // <div className={styles.consumerListContainer}>
     <div className="aigw-detail-content-wrap">
       <div className="aigw-detail-content-title">消费者管理</div>
      {/* <Tabs activeKey={activeTab} onChange={setActiveTab}> */}
        {/* <TabPane tab="消费者列表" key="list"> */}
        <div className="aigw-detail-operation-container">
          <div className="aigw-detail-operation-left">
          <div className={styles.operationBar}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Button
                type="primary"
                icon={<OutlinedPlusNew />}
                onClick={handleCreate}
                disabled={totalCount >= 200}
              >
                创建消费者
              </Button>
              <span style={{ fontSize: 12, color: '#84868C' }}>
                已创建 {totalCount} 个，最多可创建 200 个
              </span>
            </div>
            </div>
            </div>
            <div className="aigw-detail-operation-right">
            <Button
              className={styles.refreshButton}
              icon={<OutlinedRefresh />}
              onClick={handleRefresh}
            />
            </div>
          </div>

          <Table
            columns={columns}
            dataSource={consumerList}
            loading={loading}
            rowKey="consumerId"
            locale={{   emptyText: (
              <Empty 
                description={
                  <span>
                    暂未创建消费者。
                    <a onClick={handleCreate} style={{ marginLeft: '4px', cursor: 'pointer' }}>
                      <OutlinedPlusNew style={{ marginRight: '4px' }} />
                      创建消费者
                    </a>
                  </span>
                } 
              />
            )  }}
            pagination={{
              current: pageParams.pageNo,
              pageSize: pageParams.pageSize,
              total: totalCount,
              onChange: handlePageChange,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`
            }}
          />
        {/* </TabPane> */}
      {/* </Tabs> */}

      {/* 创建消费者弹窗 */}
      <Modal className='modalHeight'
        title="创建消费者"
        width={760}
        onCancel={() => setCreateDrawerVisible(false)}
        visible={createDrawerVisible}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button
              onClick={() => setCreateDrawerVisible(false)}
              style={{ marginRight: 8 }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleCreateSubmit}
              loading={submitting}
            >
              确定
            </Button>
          </div>
        }
      >
        <Form
          form={createForm}
          layout="horizontal"
          labelCol={{ span: 3 }}
          labelAlign="left"
          inputMaxWidth="100%"
        >
          {/* 消费者信息模块 */}
          <div className={styles.formSection}>
            {/* <div className={styles.sectionTitle}>消费者信息</div> */}
            <div className={styles.formContent}>
              <Form.Item
                name="consumerName"
                label="消费者名称"
                validateTrigger={['onChange']}
                extra={
                  <span style={{ whiteSpace: 'nowrap' }}>
                    <span style={{ fontSize: 12, color: '#FF9326' }}>创建后不支持修改。</span>
                    <span>
                      支持大小写字母、数字以及-_特殊字符，长度限制2-64个字符
                    </span>
                  </span>
                }
                rules={[
                  { required: true, message: '请输入消费者名称' },
                  {
                    pattern: /^[a-zA-Z0-9_-]{2,64}$/,
                    message: '消费者名称长度2-64，支持大小写字母、数字以及-_特殊字符'
                  },
                ]}
                validateStatus={nameExists ? 'error' : undefined}
                help={nameExists ? '已存在同名消费者' : undefined}
              >
                <Input
                  value={consumerNameInput}
                  placeholder="请输入消费者名称"
                  maxLength={64}
                  limitLength={64}
                   style={{ width: 400 }}
                  onChange={e => {
                    const val = e.target.value;
                    setConsumerNameInput(val);
                    // 立刻清除上一次的校验错误
                    setNameExists(false);
                  }}
                  onBlur={() => {
                    // 失焦时也做一次校验
                    validateConsumerName(consumerNameInput);
                  }}
                  suffix={nameValidating ? <OutlinedRefresh style={{ animation: 'spin 1s linear infinite' }} /> : null}
                />
              </Form.Item>

              <Form.Item
                name="description"
                label="描述"
                rules={[
                  { max: 64, message: '描述最多64个字符' }
                ]}
              >
                <Input
                  placeholder="请输入消费者的描述信息"
                  maxLength={64}
                  limitLength={64}
                  style={{ width: 400 }}
                />
              </Form.Item>

              <Form.Item
                name="authType"
                label="认证方式"
                initialValue="KeyAuth"
                rules={[{ required: true, message: '请选择认证方式' }]}
              // style={{marginBottom: 0}}
              >
                <Radio.Group>
                  {/* <Radio.Button value="JWT">JWT</Radio.Button> */}
                  <Radio.Button value="KeyAuth">Key Auth</Radio.Button>
                </Radio.Group>
              </Form.Item>

              {/* 限额配置 */}
              <Form.Item
                name="quotaType"
                label="限额配置"
                initialValue="unlimited"
                rules={[{ required: true, message: '请选择限额配置' }]}
                style={{ marginBottom: 24 }}
              >
                <Radio.Group
                  value={quotaType}
                  onChange={(e) => {
                    if (e && e.target && 'value' in e.target) {
                      const value = e.target.value as 'unlimited' | 'custom';
                      setQuotaType(value);
                      // 同时更新表单字段值
                      createForm.setFieldsValue({ quotaType: value });
                    }
                  }}
                >
                  <Radio.Button value="unlimited">无配额限制</Radio.Button>
                  <Radio.Button value="custom">自定义配额</Radio.Button>
                </Radio.Group>
              </Form.Item>

              {/* Token配额配置 - 仅在自定义配额时显示 */}
              {quotaType === 'custom' && (
                <Form.Item
                  name="totalQuota"
                  label="Token 配额"
                  rules={[
                    { required: true, message: '请设置 Token 配额' },
                    { 
                      validator: (_, value) => {
                        if (quotaType === 'custom' && (value === null || value === undefined || value < 0)) {
                          return Promise.reject(new Error('Token 配额不能少于0'));
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                  style={{ marginBottom: 24 }}
                >
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 8,
                    alignSelf: 'stretch'
                  }}>
                    <div style={{ 
                      width: '60%',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <Slider
                        min={0}
                        max={100}
                        value={Math.floor(totalQuota / 10000)}
                        onChange={(value: number) => {
                          // 确保滑块值为整数
                          const intValue = Math.floor(value);
                          const newValue = intValue * 10000;
                          setTotalQuota(newValue);
                          // 同时更新表单字段值
                          createForm.setFieldsValue({ totalQuota: intValue });
                        }}
                        style={{ width: '100%' }}
                      />
                    </div>
                    <InputNumber
                      min={0}
                      max={100}
                      precision={0}
                      value={totalQuota / 10000}
                      onChange={(value) => {
                        // 确保输入值为非负整数
                        const intValue = Math.max(0, Math.floor(value || 0));
                        const newValue = intValue * 10000; // 允许0万
                        setTotalQuota(newValue);
                        // 同时更新表单字段值
                        createForm.setFieldsValue({ totalQuota: intValue });
                      }}
                      onBlur={(e) => {
                        // 失焦时确保最小值为0
                        const currentValue = parseInt(e.target.value) || 0;
                        if (currentValue < 0) {
                          const newValue = 0;
                          setTotalQuota(newValue);
                          createForm.setFieldsValue({ totalQuota: 0 });
                        }
                      }}
                      style={{ 
                        width: 78,
                        height: 32,
                        fontSize: 12,
                        color: '#151B26'
                      }}
                    />
                    <span style={{ 
                      color: '#979797', 
                      fontSize: 12,
                      lineHeight: '20px',
                      fontFamily: 'PingFang SC'
                    }}>万</span>
                  </div>
                </Form.Item>
              )}

              {/* 消费者授权模块 */}
              <Form.Item
                label="授权路由"
                name="routeIds"
                className={styles.fixFlexControl}
                extra={
                  <>
                    <span style={{ fontSize: 12, color: '#FF9326' }}>注意：</span>
                    <span>授权后，所选路由需开启消费者认证才能生效。</span>
                  </>
                }
              >
                <Transfer
                  dataSource={getTransferDataSource()}
                  targetKeys={selectedRouteIds}
                  leftStyle={{ width: 240 }}
                  rightStyle={{ width: 240 }}
                  locale={{
                    searchPlaceholder: '请输入路由名称搜索',
                    sourceTitle: '可选路由',
                  }}
                  onChange={handleRouteChange}
                  render={item => (
                    <Tooltip title={item.title} placement="topLeft">
                      <div className={styles.transferItem}>
                        <div className={styles.transferItemTitle}>
                          {item.title}
                        </div>
                        {!item.authEnabled && (
                          <Tag className={styles.unAuthTag}>未开启认证</Tag>
                        )}
                      </div>
                    </Tooltip>
                  )}
                  showSearch
                />
              </Form.Item>
            </div>
          </div>

        </Form>
      </Modal>

      {/* 编辑消费者弹窗 */}
      <Modal
        className='modalHeight'
        title="编辑消费者"
        width={760}
        onCancel={() => setEditDrawerVisible(false)}
        visible={editDrawerVisible}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button
              onClick={() => setEditDrawerVisible(false)}
              style={{ marginRight: 8 }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleEditSubmit}
              loading={editSubmitting}
            >
              确定
            </Button>
          </div>
        }
      >
        <Loading loading={consumerDetailLoading}>
          <Form
            form={editForm}
            layout="horizontal"
            labelCol={{ span: 3 }}
            labelAlign="left"
            inputMaxWidth="100%"
          >
            {/* 消费者信息模块 */}
            <div className={styles.formSection}>
              <div className={styles.formContent}>
                <Form.Item
                  name="consumerName"
                  label="消费者名称"
                >
                  <div className={styles.readonlyField}>{currentConsumer?.consumerName || '-'}</div>
                </Form.Item>

                <Form.Item
                  name="description"
                  label="描述"
                  rules={[
                    { max: 64, message: '描述最多64个字符' }
                  ]}
                >
                  <Input
                    placeholder="请输入消费者的描述信息"
                    maxLength={64}
                    limitLength={64}
                    style={{ width: 400 }}
                  />
                </Form.Item>

                <Form.Item
                  name="authType"
                  label="认证方式"
                >
                  <div className={styles.readonlyField}>{currentConsumer?.authType || '-'}</div>
                </Form.Item>

                <Form.Item
                  label="认证信息"
                >
                  <div className={styles.readonlyField}>
                    <Tooltip title="点击复制认证Token">
                      <span style={{ cursor: 'pointer' }} onClick={copyAuthToken}>
                        {currentConsumer?.authInfo?.token || '-'}
                        {currentConsumer?.authInfo?.token && (
                          <OutlinedCopy width={16} style={{ marginLeft: 8, cursor: 'pointer', color: 'rgb(36, 104, 242)' }} />
                        )}
                      </span>
                    </Tooltip>
                  </div>
                </Form.Item>

                {/* 限额配置 */}
                <Form.Item
                  name="quotaType"
                  label="限额配置"
                  rules={[{ required: true, message: '请选择限额配置' }]}
                  style={{ marginBottom: 24 }}
                >
                  <Radio.Group
                    value={editQuotaType}
                    onChange={(e) => {
                      if (e && e.target && 'value' in e.target) {
                        const value = e.target.value as 'unlimited' | 'custom';
                        setEditQuotaType(value);
                        // 同时更新表单字段值
                        editForm.setFieldsValue({ quotaType: value });
                      }
                    }}
                  >
                    <Radio.Button value="unlimited">无配额限制</Radio.Button>
                    <Radio.Button value="custom">自定义配额</Radio.Button>
                  </Radio.Group>
                </Form.Item>

                {/* Token配额配置 - 仅在自定义配额时显示 */}
                {editQuotaType === 'custom' && (
                  <Form.Item
                    name="totalQuota"
                    label="Token 配额"
                    rules={[
                      { required: true, message: '请设置 Token 配额' },
                      { 
                        validator: (_, value) => {
                          if (editQuotaType === 'custom' && (value === null || value === undefined || value < 0)) {
                            return Promise.reject(new Error('Token 配额不能少于0'));
                          }
                          return Promise.resolve();
                        }
                      }
                    ]}
                    style={{ marginBottom: 24 }}
                  >
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: 8,
                      width: 348
                    }}>
                      <div style={{ 
                        flex: 1,
                        display: 'flex',
                        alignItems: 'center'
                      }}>
                        <Slider
                          min={0}
                          max={100}
                          value={Math.floor(editTotalQuota / 10000)}
                          onChange={(value: number) => {
                            // 确保滑块值为整数
                            const intValue = Math.floor(value);
                            const newValue = intValue * 10000;
                            setEditTotalQuota(newValue);
                            // 同时更新表单字段值
                            editForm.setFieldsValue({ totalQuota: intValue });
                          }}
                          style={{ width: '100%' }}
                        />
                      </div>
                      <InputNumber
                        min={0}
                        max={100}
                        precision={0}
                        value={editTotalQuota / 10000}
                                                  onChange={(value) => {
                            // 确保输入值为非负整数
                            const intValue = Math.max(0, Math.floor(value || 0));
                            const newValue = intValue * 10000; // 允许0万
                            setEditTotalQuota(newValue);
                            // 同时更新表单字段值
                            editForm.setFieldsValue({ totalQuota: intValue });
                          }}
                          onBlur={(e) => {
                            // 失焦时确保最小值为0
                            const currentValue = parseInt(e.target.value) || 0;
                            if (currentValue < 0) {
                              const newValue = 0;
                              setEditTotalQuota(newValue);
                              editForm.setFieldsValue({ totalQuota: 0 });
                            }
                          }}
                        style={{ 
                          width: 78,
                          height: 32,
                          fontSize: 12,
                          color: '#151B26'
                        }}
                      />
                      <span style={{ 
                        color: '#979797', 
                        fontSize: 12,
                        lineHeight: '20px',
                        fontFamily: 'PingFang SC'
                      }}>万</span>
                    </div>
                  </Form.Item>
                )}

                {/* 授权路由 */}
                <Form.Item
                  label="授权路由"
                  name="routeIds"
                  className={styles.fixFlexControl}
                  extra={
                    <>
                      <span style={{ fontSize: 12, color: '#FF9326' }}>注意：</span>
                      <span>授权后，所选路由需开启消费者认证才能生效。</span>
                    </>
                  }
                >
                  <Transfer
                    dataSource={getTransferDataSource()}
                    targetKeys={editSelectedRouteIds}
                    leftStyle={{ width: 240 }}
                    rightStyle={{ width: 240 }}
                    locale={{
                      searchPlaceholder: '请输入路由名称搜索',
                      sourceTitle: '可选路由',
                    }}
                    onChange={handleEditRouteChange}
                    render={item => (
                      <Tooltip title={item.title} placement="topLeft">
                        <div className={styles.transferItem}>
                          <div className={styles.transferItemTitle}>
                            {item.title}
                          </div>
                          {!item.authEnabled && (
                            <Tag className={styles.unAuthTag}>未开启认证</Tag>
                          )}
                        </div>
                      </Tooltip>
                    )}
                    showSearch
                  />
                </Form.Item>
              </div>
            </div>
          </Form>
        </Loading>
      </Modal>

      {/* 消费者详情抽屉 */}
      <Drawer
        title="消费者详情"
        width={600}
        onClose={() => setDetailDrawerVisible(false)}
        visible={detailDrawerVisible}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button
              onClick={() => setDetailDrawerVisible(false)}
            >
              关闭
            </Button>
          </div>
        }
      >
        <Loading loading={detailLoading}>
          {/* 消费者信息模块 */}
          <div className={styles.formSection}>
            {/* <div className={styles.sectionTitle}>消费者信息</div> */}
            <div className={styles.formContent}>
            <Form layout="horizontal" labelCol={{ span: 3 }} labelAlign="left">
              <Form.Item
                label="消费者名称"
              >
                <div className={styles.readonlyField}>{consumerDetail?.consumerName || '-'}</div>
              </Form.Item>

              <Form.Item
                label="描述"
              >
                <Tooltip title={consumerDetail?.description || '-'}>
                  <span
                    className={styles.readonlyField}
                    style={{
                      display: 'inline-block',
                      maxWidth: 400,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      verticalAlign: 'middle'
                    }}
                  >
                    {consumerDetail?.description || '-'}
                  </span>
                </Tooltip>
              </Form.Item>

              <Form.Item
                label="认证方式"
              >
                <div className={styles.readonlyField}>{consumerDetail?.authType || '-'}</div>
              </Form.Item>

              <Form.Item 
                label="认证信息"
              >

                <Tooltip title="点击复制认证Token">
                  <span style={{ cursor: 'pointer' ,marginLeft: 8,color: '#151B26'}} onClick={copyDetailAuthToken}>
                    {consumerDetail?.authInfo?.token || '-'}
                    {consumerDetail?.authInfo?.token && (
                          <OutlinedCopy width={16} style={{ marginLeft: 8, cursor: 'pointer', color: 'rgb(36, 104, 242)' }} />
                        )}
                  </span>
                </Tooltip>

              </Form.Item>

              {/* 配额信息 */}
              <Form.Item 
                label="配额信息"
              >
                <div style={{ marginLeft: 8 }}>
                  {consumerDetail?.unlimitedQuota ? (
                    <div style={{ color: '#151B26', fontSize: 12 }}>无配额限制</div>
                  ) : (
                    <div style={{paddingTop: 8}}>
                      <Progress
                        percent={Math.round(((consumerDetail?.quotaValue || 0) / (consumerDetail?.totalQuota || 1)) * 100)}
                        format={(percent) => {
                          const remaining = consumerDetail?.quotaValue || 0;
                          const totalQuota = consumerDetail?.totalQuota || 0;
                          // return `剩余 ${remaining.toLocaleString()} / 总计 ${totalQuota.toLocaleString()}`;
                        }}
                        style={{ width: 400 }}
                      />
                      <div style={{ 
                        marginTop: 8, 
                        fontSize: 12, 
                        color: '#84868C',
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: 400
                      }}>
                        <span>剩余配额：{(consumerDetail?.quotaValue || 0).toLocaleString()} Tokens</span>
                        <span>总配额：{(consumerDetail?.totalQuota || 0).toLocaleString()} Tokens</span>
                      </div>
                    </div>
                  )}
                </div>
              </Form.Item>

              <Form.Item label="已授权路由">
                {/* 已授权路由模块 */}

                <Table style={{marginLeft: 8}}
                  columns={[
                    {
                      title: '路由名称',
                      dataIndex: 'routeName',
                      key: 'routeName',
                      render: (text: string) => (
                        <a
                          style={{ cursor: 'pointer', color: '#2468f2' }}
                          onClick={() => {
                            // 跳转到路由详情页
                            window.location.hash = `/route/detail?instanceId=${instanceId}&routeName=${text}`;
                          }}
                        >
                          {text}
                        </a>
                      )
                    },
                    {
                      title: '创建时间',
                      dataIndex: 'createTime',
                      key: 'createTime'
                    },
                    {
                      title: '消费者认证',
                      dataIndex: 'authEnabled',
                      key: 'authEnabled',
                      render: (authEnabled: boolean) => (
                        authEnabled ?
                          <Tag color="success-status" icon={<OutlinedCheckCircle />}>已开启</Tag> :
                          <Tag color="warning-status" icon={<OutlinedInfoCircle />}>未开启</Tag>
                      )
                    }
                  ]}
                  dataSource={consumerDetail?.routes || []}
                  rowKey="routeId"
                  pagination={false}
                  locale={{ emptyText: <Empty description="暂无已授权路由" /> }}
                />

              </Form.Item>

            </Form>
            </div>
          </div>

        </Loading>


      </Drawer>

    </div>
  );
};

export default ConsumerList; 