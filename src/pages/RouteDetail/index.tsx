import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getQueryParams, useRegion } from '@baidu/bce-react-toolkit';
import { Layout, Menu, Tabs, Table, Tooltip, Tag, Space, toast, Row, Col, Divider, Loading, Modal, Checkbox, Empty, Button, Select, Transfer } from 'acud';
import { OutlinedLeft, OutlinedEditingSquare } from 'acud-icon';
import { useRequest } from 'ahooks';

import DetailPage from '@/components/DetailPage';
import { getRouteDetail, RouteDetailType, updateRoute } from '@/apis/route';
import { getConsumerList } from '@/apis/consumer';
import { getRegistrationInstance } from '@/apis/instance';
import urls from '@/utils/urls';
import styles from './index.module.less';

const { Sider, Content } = Layout;
const { TabPane } = Tabs;
const { Option } = Select;

// 创建一个限宽的Tag组件
const LimitedWidthTag = ({ children, ...props }) => {
  const tagStyle = {
    maxWidth: '260px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    display: 'inline-block'
  };
  
  return (
    <Tooltip title={children} placement="top">
      <Tag {...props}>
        <span style={tagStyle}>{children}</span>
      </Tag>
    </Tooltip>
  );
};

const RouteDetail: React.FC = () => {
  const { instanceId, routeName } = getQueryParams();
  const { region } = useRegion();
  const navigate = useNavigate();
  

  
  // 状态定义
  const [instanceDetail, setInstanceDetail] = useState<any>({});
  const [routeDetail, setRouteDetail] = useState<RouteDetailType | null>(null);
  const [activeMenuItem, setActiveMenuItem] = useState('base-info');
  const [activeTab, setActiveTab] = useState('basic-info');
  const [loading, setLoading] = useState(true);
  
  // 编辑消费者认证相关状态
  const [authModalVisible, setAuthModalVisible] = useState(false);
  const [authEnabled, setAuthEnabled] = useState(false);
  const [availableConsumers, setAvailableConsumers] = useState<any[]>([]);
  const [selectedConsumers, setSelectedConsumers] = useState<string[]>([]);
  const [originalAuthorizedConsumers, setOriginalAuthorizedConsumers] = useState<string[]>([]);
  const [authModalLoading, setAuthModalLoading] = useState(false);

  // 获取实例详情
  const fetchInstanceDetail = async () => {
    try {
      // console.log('请求实例详情，instanceId:', instanceId, '地域:', region);
      const res = await getRegistrationInstance(instanceId, region);
      // console.log('获取实例详情成功:', res);
      if (res?.success && res?.result) {
        setInstanceDetail(res.result);
      }
    } catch (error) {
      console.error('获取实例详情失败:', error);
    }
  };

  // 获取路由详情
  const { run: fetchRouteDetail } = useRequest(
    () => {
      if (!instanceId || !routeName) {
        return Promise.reject(new Error('缺少必要参数'));
      }
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      return getRouteDetail(instanceId, routeName, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.result) {
          setRouteDetail(res.result);
          // 初始化认证状态
          setAuthEnabled(res.result.authEnabled || false);
          setSelectedConsumers(res.result.allowedConsumers || []);
          setOriginalAuthorizedConsumers(res.result.allowedConsumers || []);
        }
        setLoading(false);
      },
      onError: (error) => {
        console.error('获取路由详情失败:', error);
        toast.error({
          message: '获取路由详情失败',
          duration: 3
        });
        setLoading(false);
      }
    }
  );
  
  // 获取消费者列表
  const { loading: consumersLoading, run: fetchConsumers } = useRequest(
    () => {
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      return getConsumerList(instanceId, {
        pageNo: 1,
        pageSize: 100,
        orderBy: 'createTime',
        order: 'desc'
      }, instanceRegion);
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.page?.result) {
          setAvailableConsumers(res.page.result);
        }
      },
      onError: (error) => {
        console.error('获取消费者列表失败:', error);
        toast.error({
          message: '获取消费者列表失败',
          duration: 3
        });
      }
    }
  );
  
  // 组件加载时获取实例详情
  useEffect(() => {
    if (instanceId && routeName) {
      // 先获取实例详情
      fetchInstanceDetail();
    }
  }, [instanceId, routeName]);

  // 当实例详情加载完成后，获取路由详情
  useEffect(() => {
    if (instanceDetail?.region && instanceId && routeName) {
      fetchRouteDetail();
    }
  }, [instanceDetail?.region, instanceId, routeName]);
  
  // 返回列表
  const handleBack = () => {
    navigate(`${urls.registrationBaseInfo}?instanceId=${instanceId}&activeMenu=route-config`);
  };
  
  // 左侧菜单项
  const menuItems = [
    {
      key: 'base-info',
      label: '路由详情'
    },
    // {
    //   key: 'statistics',
    //   label: '统计'
    // }
  ];
  
  // 处理菜单点击
  const handleMenuClick = (key: string) => {
    setActiveMenuItem(key);
  };
  
  // 打开编辑消费者认证弹窗
  const handleEditAuth = () => {
    // 获取消费者列表
    fetchConsumers();
    
    // 初始化状态
    if (routeDetail) {
      setAuthEnabled(routeDetail.authEnabled);
      setSelectedConsumers(routeDetail.allowedConsumers || []);
      setOriginalAuthorizedConsumers(routeDetail.allowedConsumers || []);
    }
    
    // 显示弹窗
    setAuthModalVisible(true);
  };
  
  // 处理消费者认证开关变更
  const handleAuthEnabledChange = (checked: boolean) => {
    setAuthEnabled(checked);
  };
  
  // 处理消费者选择变更
  const handleConsumerChange = (targetKeys: string[]) => {
    setSelectedConsumers(targetKeys);
  };
  
  // 保存消费者认证设置
  const handleSaveAuth = async () => {
    if (!routeDetail || !instanceId || !routeName) return;
    
    setAuthModalLoading(true);
    
    try {
      // 构建请求数据 - 确保保留现有的其他字段
      // 对于单服务模式，需要确保targetService是单个对象
      const targetServiceForUpdate = Array.isArray(routeDetail.targetService) 
        ? routeDetail.targetService[0] 
        : routeDetail.targetService;
        
      const requestData = {
        matchRules: routeDetail.matchRules,
        targetService: targetServiceForUpdate,
        authEnabled: authEnabled,
        allowedConsumers: authEnabled ? selectedConsumers : []
      };
      
      // 调用更新接口
      // 使用实例的地域信息，如果实例详情还没加载完成，则使用框架的地域信息
      const instanceRegion = instanceDetail?.region || region;
      const res = await updateRoute(instanceId, routeName, requestData, instanceRegion);
      
      if (res?.success) {
        toast.success({
          message: '消费者认证配置已更新',
          duration: 3
        });
        
        // 关闭弹窗并刷新详情
        setAuthModalVisible(false);
        fetchRouteDetail();
      } else {
        // 显示错误信息
        toast.error({
          message: res?.message || '更新消费者认证失败',
          duration: 3
        });
      }
    } catch (error) {
      console.error('更新消费者认证失败:', error);
      toast.error({
        message: '更新消费者认证失败，请稍后重试',
        duration: 3
      });
    } finally {
      setAuthModalLoading(false);
    }
  };
  
  // 跳转到编辑路由页面
  const handleEdit = () => {
    navigate(`/route/edit?instanceId=${instanceId}&routeName=${routeName}`);
  };
  
  // 基本信息模块
  const renderBaseInfoModule = () => {
    if (!routeDetail) return null;
    
    return (
      <div className={styles.moduleContainer}>
        <div className={styles.moduleTitle}>基本信息</div>
        <div className={styles.moduleContent}>
          <Row gutter={[24, 16]}>
            <Col span={8}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>路由名称：</span>
                <span className={styles.infoValue}>{routeDetail.routeName}</span>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>创建时间：</span>
                <span className={styles.infoValue}>{routeDetail.createTime}</span>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>更新时间：</span>
                <span className={styles.infoValue}>{routeDetail.updateTime}</span>
              </div>
            </Col>
            <Col span={24}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>消费者认证：</span>
                <div className={styles.infoValueWithAction}>
                  <Tooltip 
                    title={
                    routeDetail.allowedConsumers && routeDetail.allowedConsumers.length > 0 
                        ? `已授权消费者: ${routeDetail.allowedConsumers.join(', ')}` 
                        : '未授权任何消费者'
                    }
                    placement="bottom"
                  >
                    <span className={styles.infoValue}>{routeDetail.authEnabled ? '已开启' : '未开启'}</span>
                  </Tooltip>
                  <OutlinedEditingSquare 
                    className={styles.editIcon} 
                    onClick={handleEditAuth}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    );
  };
  
  // 渲染编辑消费者认证弹窗
  const renderAuthModal = () => {
    return (
      <Modal
        title="编辑消费者认证"
        visible={authModalVisible}
        onCancel={() => setAuthModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setAuthModalVisible(false)}>
            取消
          </Button>,
          <Button 
            key="submit" 
            type="primary" 
            loading={authModalLoading}
            onClick={handleSaveAuth}
          >
            确定
          </Button>
        ]}
        width={740}
      >
        <div>
          <div className={styles.authModalItem}>
            <span className={styles.authModalLabel}>消费者认证</span>
            <Checkbox
              checked={authEnabled}
              onChange={(e) => handleAuthEnabledChange(e.target.checked)}
            >
              开启认证
            </Checkbox>
          </div>
          
          <div className={styles.authModalTip}>
            开启后，需配置可访问消费者，仅被授权的消费者可访问当前路由，也可在
            <a 
              style={{ color: '#2468f2', cursor: 'pointer' }}
              onClick={() => navigate(`/instance/base/info?instanceId=${instanceId}&activeMenu=consumer-list`)}
            >
              消费者管理
            </a>
            中进行授权
          </div>
          
          {authEnabled && (
            <div className={styles.authTransferContainer}>
              <Transfer
                dataSource={availableConsumers.map(item => ({
                  key: item.consumerName,
                  title: item.consumerName,
                  description: item.description || '',
                  searchText: item.consumerName
                }))}
                targetKeys={selectedConsumers}
                onChange={handleConsumerChange}
                leftStyle={{
                  width: '240px',
                  height: '286px'
                }}
                rightStyle={{
                  width: '240px',
                  height: '286px'
                }}
                render={item => (
                  <Tooltip title={item.title}>
                    <div className={styles.transferItem}>
                      <div className={styles.transferItemTitle}>
                        {item.title}
                      </div>
                      {!originalAuthorizedConsumers.includes(item.key as string) && (
                        <Tag className={styles.unAuthTag}>
                          未授权
                        </Tag>
                      )}
                    </div>
                  </Tooltip>
                )}
                showSearch
                filterOption={(inputValue, item) => {
                  return item.title?.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1;
                }}
                locale={{ 
                  searchPlaceholder: '请输入消费者名称',
                  sourceNoData: (
                    <Empty
                      imageStyle={{ height: 100 }}
                      description="暂未创建消费者"
                    />
                  )
                }}
              />
            </div>
          )}
        </div>
      </Modal>
    );
  };
  
  // 匹配规则模块
  const renderMatchRulesModule = () => {
    if (!routeDetail) return null;
    const { rewrite } = routeDetail;
    const { matchRules } = routeDetail;
    const isRewriteEnabled = rewrite?.enabled || false;
    return (
      <div className={styles.moduleContainer}>
        <div className={styles.moduleTitle}>匹配规则</div>
        <div className={styles.moduleContent}>
          <Row gutter={[24, 16]}>
            <Col span={24}>
              <div className={styles.infoItem}>
                <span className={`${styles.infoLabel} ${styles.fixedWidthLabel}`}>路径（Path）：</span>
                <div className={styles.infoValue}>
                  <span>
                    {matchRules.pathRule.matchType === 'prefix' ? '前缀匹配' : 
                     matchRules.pathRule.matchType === 'exact' ? '精确匹配' : 
                     matchRules.pathRule.matchType === 'regex' ? '正则匹配' : 
                     matchRules.pathRule.matchType}
                    {" "}
                    {matchRules.pathRule.value}
                  </span>
                </div>
              </div>
            </Col>
            <Col span={24}>
              <div className={styles.infoItem}>
                <span className={`${styles.infoLabel} ${styles.fixedWidthLabel}`}>请求方法（Method）：</span>
                <div className={styles.infoValue}>
                  <Space size={[0, 8]} wrap>
                    {matchRules.methods && matchRules.methods.map((method) => (
                      <Tag 
                        key={method}
                        className={`${styles.methodTag} ${styles[`methodTag-${method}`]}`}
                      >
                        {method}
                      </Tag>
                    ))}
                  </Space>
                </div>
              </div>
            </Col>
            <Col span={24}>
              <div className={styles.infoItem}>
                <span className={`${styles.infoLabel} ${styles.fixedWidthLabel}`}>请求头（Header）：</span>
                <div className={styles.infoValue}>
                  <Space size={[0, 8]} wrap>
                    {matchRules.headers && matchRules.headers.map((header, index) => (
                      <LimitedWidthTag key={index}>
                        {`${header.key} ${header.matchType === 'exact' ? '精确匹配' : '前缀匹配'} ${header.value}`}
                      </LimitedWidthTag>
                    ))}
                    {(!matchRules.headers || matchRules.headers.length === 0) && <span>无</span>}
                  </Space>
                </div>
              </div>
            </Col>
            <Col span={24}>
              <div className={styles.infoItem}>
                <span className={`${styles.infoLabel} ${styles.fixedWidthLabel}`}>请求参数（Query）：</span>
                <div className={styles.infoValue}>
                  <Space size={[0, 8]} wrap>
                    {matchRules.queryParams && matchRules.queryParams.map((param, index) => (
                      <LimitedWidthTag key={index}>
                        {`${param.key} ${param.matchType === 'exact' ? '精确匹配' : '前缀匹配'} ${param.value}`}
                      </LimitedWidthTag>
                    ))}
                    {(!matchRules.queryParams || matchRules.queryParams.length === 0) && <span>无</span>}
                  </Space>
                </div>
              </div>
            </Col>
            <Col span={24}>
              <div className={styles.infoItem_last}>
                <span className={`${styles.infoLabel} ${styles.fixedWidthLabel}`}>路径重写：</span>
                <div className={styles.infoValue}>
                  {isRewriteEnabled ? (
                    <span>已开启，重写模式：标准，重写路径：{rewrite?.path || '-'}</span>
                  ) : (
                    '未开启'
                  )}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    );
  };
  

  
  // 目标服务模块
  const renderTargetServiceModule = () => {
    if (!routeDetail) return null;
    
    const { targetService, multiService, trafficDistributionStrategy } = routeDetail;
    
    // 判断是否为多服务
    const isMultiService = multiService || Array.isArray(targetService);
    
    // 基础列定义
    const baseColumns = [
      {
        title: '服务名称',
        dataIndex: 'serviceName',
        key: 'serviceName'
      },
      {
        title: '服务端口',
        dataIndex: 'servicePort',
        key: 'servicePort'
      },
      {
        title: '服务来源',
        dataIndex: 'serviceSource',
        key: 'serviceSource'
      },
      {
        title: '命名空间',
        dataIndex: 'namespace',
        key: 'namespace'
      },
      {
        title: '负载均衡算法',
        dataIndex: 'loadBalanceAlgorithm',
        key: 'loadBalanceAlgorithm',
        render: (value: string, record: any) => {
          const algorithmMap = {
            'round-robin': '轮询',
            'random': '随机',
            'least-conn': '最小连接数',
            'consistent-hash': '哈希一致性'
          };
          
          const algorithmName = algorithmMap[value] || value;
          
          // 如果是哈希一致性算法，显示详细信息
          if (value === 'consistent-hash' && record.hashType) {
            const hashTypeMap = {
              'header': '基于Header',
              'query_param': '基于请求参数',
              'ip': '基于源IP',
              'cookie': '基于Cookie'
            };
            
            const hashTypeName = hashTypeMap[record.hashType] || record.hashType;
            const hashKey = record.hashKey || '';
            
            // 基于源IP时不显示hashKey
            if (record.hashType === 'ip') {
              return (
                <div>
                  <div>{algorithmName}</div>
                  <div style={{ color: '#8C8C8C', fontSize: '12px' }}>
                    哈希方式：{hashTypeName}
                  </div>
                </div>
              );
            } else {
              return (
                <div>
                  <div>{algorithmName}</div>
                  <div style={{ color: '#8C8C8C', fontSize: '12px' }}>
                    哈希方式：{hashTypeName}
                  </div>
                  <div style={{ color: '#8C8C8C', fontSize: '12px' }}>
                    哈希参数：{hashKey}
                  </div>
                </div>
              );
            }
          }
          
          return algorithmName;
        }
      }
    ];
    
    // 根据服务类型和分发策略添加额外列
    let columns = [...baseColumns];
    
    if (isMultiService) {
      if (trafficDistributionStrategy === 'ratio') {
        // 按比例分发，添加请求比例列
        columns.push({
          title: '请求比例',
          dataIndex: 'requestRatio',
          key: 'requestRatio',
          render: (value: any) => value ? `${value}%` : '-'
        });
      } else if (trafficDistributionStrategy === 'model_name') {
        // 按模型名称分发，添加模型名称列
        columns.push({
          title: '模型名称',
          dataIndex: 'modelName',
          key: 'modelName',
          render: (value: any) => value || '-'
        });
      }
    }
    
    // 构建表格数据源
    let dataSource: any[] = [];
    
    if (isMultiService && Array.isArray(targetService)) {
      // 多服务模式
      dataSource = targetService.map((service, index) => ({
        key: index.toString(),
        ...service
      }));
    } else {
      // 单服务模式
      dataSource = [
        {
          key: '1',
          ...targetService
        }
      ];
    }
    
    return (
      <div className={styles.moduleContainer}>
        <div className={styles.moduleTitle}>目标服务</div>
        <div className={styles.moduleContent}>
          <Table
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            size="middle"
          />
        </div>
      </div>
    );
  };
  
  // 高级策略模块
  const renderAdvancedPolicyModule = () => {
    if (!routeDetail) return null;
    
    const { tokenRateLimit, timeoutPolicy, retryPolicy } = routeDetail;
    
    // 检查是否开启了Token限流
    const isTokenRateLimitEnabled = tokenRateLimit?.enabled || false;
    
    // 检查是否开启了超时策略
    const isTimeoutPolicyEnabled = timeoutPolicy?.enabled || false;
    
    // 检查是否开启了重试策略
    const isRetryPolicyEnabled = retryPolicy?.enabled || false;
    
    // 限流类型映射
    const limitTypeMap = {
      'consumer': '按消费者',
      'header': '按请求头',
      'query_param': '按请求参数'
    };
    
    // 时间单位映射
    const timeUnitMap = {
      'second': '每秒',
      'minute': '每分钟',
      'hour': '每小时',
      'day': '每天'
    };
    
    // 重试条件映射
    const retryConditionMap = {
      '5xx': '5xx',
      'reset': 'reset',
      'connect-failure': 'connect-failure',
      'refused-stream': 'refused-stream',
      'retriable-status-codes': 'retriable-status-codes',
      'cancelled': 'cancelled',
      'deadline-exceeded': 'deadline-exceeded',
      'internal': 'internal',
      'resource-exhausted': 'resource-exhausted',
      'unavailable': 'unavailable',
      'gateway-error': 'gateway-error'
    };
    
    // Token限流表格列定义
    const tokenRateLimitColumns = [
      {
        title: '限流类型',
        dataIndex: 'limitType',
        key: 'limitType',
        render: (value: string) => limitTypeMap[value] || value
      },
      {
        title: '限流条件',
        dataIndex: 'limitCondition',
        key: 'limitCondition'
      },
      {
        title: '时间单位',
        dataIndex: 'timeUnit',
        key: 'timeUnit',
        render: (value: string) => timeUnitMap[value] || value
      },
      {
        title: 'Token数量',
        dataIndex: 'tokenAmount',
        key: 'tokenAmount'
      }
    ];
    
    // 构建Token限流表格数据源
    let tokenRateLimitDataSource: any[] = [];
    
    if (isTokenRateLimitEnabled && tokenRateLimit?.rule_items) {
      tokenRateLimitDataSource = tokenRateLimit.rule_items.map((item, index) => {
        let limitCondition = '';
        
        // 根据限流类型构建限流条件显示文本
        if (item.match_condition.type === 'consumer') {
          limitCondition = item.match_condition.value;
        } else if (item.match_condition.type === 'header') {
          limitCondition = `${item.match_condition.key}: ${item.match_condition.value}`;
        } else if (item.match_condition.type === 'query_param') {
          limitCondition = `${item.match_condition.key}=${item.match_condition.value}`;
        }
        
        return {
          key: index.toString(),
          limitType: item.match_condition.type,
          limitCondition: limitCondition,
          timeUnit: item.limit_config.time_unit,
          tokenAmount: item.limit_config.token_amount
        };
      });
    }
    
    // 格式化重试条件
    const formatRetryConditions = (conditions: string) => {
      if (!conditions) return '-';
      
      const conditionList = conditions.split(',').map(condition => condition.trim());
      return conditionList.map(condition => retryConditionMap[condition] || condition).join(', ');
    };
    
    return (
      <div className={styles.moduleContainer}>
        <div className={styles.moduleTitle}>高级策略</div>
        <div className={styles.moduleContent}>
          <Row gutter={[24, 16]}>
            {/* Token 限流 */}
            <Col span={24}>
              <div className={styles.infoItem}>
                <span className={`${styles.infoLabel} ${styles.fixedWidthLabel}`}>Token 限流：</span>
                <div className={styles.infoValue}>
                  {isTokenRateLimitEnabled ? '已开启' : '未开启'}
                </div>
              </div>
              {isTokenRateLimitEnabled && (
                <div style={{ marginTop: '16px', width: '100%' }}>
                  <Table
                    columns={tokenRateLimitColumns}
                    dataSource={tokenRateLimitDataSource}
                    pagination={false}
                    size="middle"
                    style={{ width: '100%' }}
                  />
                </div>
              )}
            </Col>
            
            {/* 超时策略 */}
            <Col span={24}>
              <div className={styles.infoItem}>
                <span className={`${styles.infoLabel} ${styles.fixedWidthLabel}`}>超时策略：</span>
                <div className={styles.infoValue}>
                  {isTimeoutPolicyEnabled ? (
                    <span>已开启，超时时间：{timeoutPolicy?.timeout || 0} 秒</span>
                  ) : (
                    '未开启'
                  )}
                </div>
              </div>
            </Col>
            
            {/* 重试策略 */}
            <Col span={24}>
              <div className={styles.infoItem_last}>
                <span className={`${styles.infoLabel} ${styles.fixedWidthLabel}`}>重试策略：</span>
                <div className={styles.infoValue}>
                  {isRetryPolicyEnabled ? (
                    <span>已开启，重试条件：{formatRetryConditions(retryPolicy?.retryConditions || '')}，重试次数：{retryPolicy?.numRetries || 0} 次</span>
                  ) : (
                    '未开启'
                  )}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    );
  };
  
  // 统计模块（占位）
  const renderStatisticsModule = () => {
    return (
      <div className={styles.moduleContainer}>
        <div className={styles.moduleTitle}>统计数据</div>
        <div className={styles.moduleContent}>
          <Row>
            <Col span={24}>
              <div className={styles.placeholder}>
                统计数据展示区域（待实现）
              </div>
            </Col>
          </Row>
        </div>
      </div>
    );
  };
  
  // 根据菜单项渲染内容
  const renderContentByMenuKey = (key: string) => {
    switch (key) {
      case 'base-info':
        return (
          <div className={styles.moduleWrapper}>
            {renderBaseInfoModule()}
            {renderMatchRulesModule()}
         
            {renderTargetServiceModule()}
            {renderAdvancedPolicyModule()}
          </div>
        );
      case 'statistics':
        return renderStatisticsModule();
      default:
        return null;
    }
  };

  const siderStyle = {
    background: '#fff',
    boxShadow: 'none'
  };
  
  const emptyPanesData = [];

  return (
    <div className={styles.routeDetailContainer}>
      <DetailPage
        mode="vertical"
        backUrl={`#${urls.registrationBaseInfo}?instanceId=${instanceId}&activeMenu=route-config`}
        headerName={`${instanceId || ''} / ${routeName || ''}`}
        statusText="已发布"
        statusClassName="circle status-success"
        isCustomContent={true}
        panesData={emptyPanesData}
        headerOperations={
          <Button 
          
            onClick={handleEdit}
          >
            编辑
          </Button>
        }
      >
        <div className={styles.routeDetailLayout}>
          <Sider 
            width={180} 
            className={styles.routeDetailSider}
            theme="light"
            style={siderStyle}
          >
            <Menu
              mode="inline"
              selectedKeys={[activeMenuItem]}
              className={styles.routeDetailMenu}
              onSelect={({key}) => handleMenuClick(key)}
              style={{ height: '100%', borderRight: 'none', boxShadow: 'none' }}
            >
              {menuItems.map((item) => (
                <Menu.Item key={item.key}>{item.label}</Menu.Item>
              ))}
            </Menu>
          </Sider>
          <Content className={styles.routeDetailContent}>
            {loading ? (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
                <Loading loading={true} size="large" tip="加载中..." />
              </div>
            ) : (
              renderContentByMenuKey(activeMenuItem)
            )}
          </Content>
        </div>
      </DetailPage>
      
      {/* 编辑消费者认证弹窗 */}
      {renderAuthModal()}
    </div>
  );
};

export default RouteDetail; 