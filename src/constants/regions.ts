/**
 * 地域配置常量
 * 包含所有支持的地域及其显示名称映射
 */

// 地域代码类型定义
export type RegionCode = 'bj' | 'gz' | 'su' | 'bd' | 'cd' | 'yq';

// 地域信息接口
export interface RegionInfo {
  code: RegionCode;
  name: string;
  displayName: string;
}

// 地域配置映射
export const REGION_CONFIG: Record<RegionCode, RegionInfo> = {
  bj: {
    code: 'bj',
    name: '华北 - 北京',
    displayName: '华北 - 北京'
  },
  gz: {
    code: 'gz',
    name: '华南 - 广州',
    displayName: '华南 - 广州'
  },
  su: {
    code: 'su',
    name: '华东 - 苏州',
    displayName: '华东 - 苏州'
  },
  bd: {
    code: 'bd',
    name: '华北 - 保定',
    displayName: '华北 - 保定'
  },
  cd: {
    code: 'cd',
    name: '西南 - 成都',
    displayName: '西南 - 成都'
  },
  yq: {
    code: 'yq',
    name: '华北 - 阳泉',
    displayName: '华北 - 阳泉'
  }
};

// 地域选项数组（用于下拉选择器等）
export const REGION_OPTIONS: Array<{label: string; value: RegionCode}> = [
  { label: '华北 - 北京', value: 'bj' },
  { label: '华南 - 广州', value: 'gz' },
  { label: '华东 - 苏州', value: 'su' },
  { label: '华北 - 保定', value: 'bd' },
  { label: '西南 - 成都', value: 'cd' },
  { label: '华北 - 阳泉', value: 'yq' }
];

// 地域代码数组
export const REGION_CODES: RegionCode[] = ['bj', 'gz', 'su', 'bd', 'cd', 'yq'];

// 获取地域显示名称的工具函数
export const getRegionDisplayName = (regionCode: string): string => {
  const region = REGION_CONFIG[regionCode as RegionCode];
  return region ? region.displayName : regionCode || '-';
};

// 检查地域代码是否有效的工具函数
export const isValidRegionCode = (regionCode: string): regionCode is RegionCode => {
  return REGION_CODES.includes(regionCode as RegionCode);
};

// 默认地域
export const DEFAULT_REGION: RegionCode = 'bj';
