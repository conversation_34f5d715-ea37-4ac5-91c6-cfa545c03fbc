---
type: "always_apply"
---


# ACUD常用组件详解

## Button 按钮

通过设置 Button 的属性来产生不同的按钮样式，推荐顺序为：`type` -> `size` -> `loading` -> `disabled`。

### 属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 按钮类型 | `primary` \| `text` \| `actiontext` \| `highlight` \| `enhance` \| `default` | `default` |
| size | 按钮大小 | `large` \| `middle` \| `small` | `middle` |
| loading | 设置载入状态 | boolean \| { delay: number } | false |
| disabled | 按钮失效状态 | boolean | false |
| block | 将按钮宽度调整为其父宽度 | boolean | false |
| href | 点击跳转的地址，指定此属性按钮的行为和链接一致 | string | - |
| htmlType | 设置原生 `type` 值 | string | `button` |
| icon | 设置按钮图标组件 | ReactNode | - |
| target | 相当于 a 链接的 target 属性，href 存在时生效 | string | - |
| onClick | 点击按钮的回调 | (event) => void | - |


## Link 链接

### 属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 链接类型 | `primary` \| `text` \| `default` | `default` |
| size | 链接大小 | `medium` \| `small` | `small` |
| disabled | 失效状态 | boolean | false |
| href | 点击跳转地址 | string | - |
| icon | 图标组件 | ReactNode | - |
| target | 相当于 a 链接的 target 属性 | string | - |
| onClick | 点击回调 | (event) => void | - |

## Divider 分割线

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| children | 嵌套的标题 | ReactNode | - |
| className | 分割线样式类 | string | - |
| dashed | 是否虚线 | boolean | false |
| orientation | 分割线标题位置 | `left` \| `right` \| `center` | `center` |
| orientationMargin | 标题和边框之间的距离 | string \| number | - |
| plain | 文字是否为普通正文样式 | boolean | false |
| style | 分割线样式对象 | CSSProperties | - |
| type | 水平还是垂直类型 | `horizontal` \| `vertical` | `horizontal` |

## Menu 菜单

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| mode | 菜单类型 | `vertical` \| `horizontal` \| `inline` | `vertical` |
| scope | 作用域 | 'local'\| 'global' | 'local' |
| defaultOpenKeys | 初始展开的 SubMenu 菜单项 key 数组 | string[] | - |
| defaultSelectedKeys | 初始选中的菜单项 key 数组 | string[] | - |
| openKeys | 当前展开的 SubMenu 菜单项 key 数组 | string[] | - |
| selectedKeys | 当前选中的菜单项 key 数组 | string[] | - |
| multiple | 是否允许多选 | boolean | false |
| inlineCollapsed | inline 时菜单是否收起状态 | boolean | - |
| inlineIndent | inline 模式的菜单缩进宽度 | number | 14 |
| selectable | 是否允许选中 | boolean | true |
| onClick | 点击 MenuItem 调用此函数 | function ({ item, key, keyPath, domEvent }) | - |
| onSelect | 被选中时调用 | function ({ item, key, keyPath, selectedKeys, domEvent }) | - |
| onOpenChange | SubMenu 展开/关闭的回调 | function (openKeys: string[]) | - |

### 业务 header 菜单

mode="horizontal" scope="global" 时，支持配置：

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| logoInfo | 菜单图标 | {logo: string \| ReactNode; urlConfig: {href: string; target?: string}} | - |
| titleInfo | 菜单标题 | {title: string \| ReactNode; urlConfig: {href: string; target?: string}} | - |
| headerMenu | 菜单导航栏 | ReactNode | - |
| viceHeaderMenu | 副菜单导航栏 | ReactNode | - |
| otherArea | 右侧信息栏 | ReactNode | - |
| headerStyle | 自定义菜单样式 | any | - |

### 子组件

- `Menu.MenuHead` - 菜单头部
- `Menu.Item` - 菜单项
- `Menu.SubMenu` - 子菜单
- `Menu.ItemGroup` - 菜单项分组
- `Menu.Divider` - 菜单分割线
- `Menu.ItemCustom` - 自定义菜单项

## Steps 步骤条

基本用法：
```jsx
<Steps>
  <Step title="第一步" />
  <Step title="第二步" />
  <Step title="第三步" />
</Steps>
```

### Steps 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| current | 指定当前步骤，从 0 开始记数 | number | 0 |
| direction | 指定步骤条方向 | `horizontal` \| `vertical` | `horizontal` |
| size | 指定大小 | `default` \| `small` | `default` |
| status | 指定当前步骤的状态 | `wait` \| `process` \| `finish` \| `error` | `process` |
| type | 步骤条类型 | `default` \| `navigation` | `default` |
| onChange | 点击切换步骤时触发 | (current) => void | - |

### Step 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| title | 标题 | ReactNode | - |
| description | 步骤的详情描述 | ReactNode | - |
| disabled | 禁用点击 | boolean | false |
| icon | 步骤图标 | ReactNode | - |
| status | 指定状态 | `wait` \| `process` \| `finish` \| `error` | `wait` |
| subTitle | 子标题 | ReactNode | - |

## Pagination 分页

```jsx
<Pagination onChange={onChange} total={50} />
```

### 属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| current | 当前页数 | number | - |
| pageSize | 每页条数 | number | - |
| total | 数据总数 | number | 0 |
| defaultCurrent | 默认的当前页数 | number | 1 |
| defaultPageSize | 默认的每页条数 | number | 10 |
| disabled | 禁用分页 | boolean | - |
| hideOnSinglePage | 只有一页时是否隐藏分页器 | boolean | false |
| showSizeChanger | 是否展示 pageSize 切换器 | boolean | - |
| showQuickJumper | 是否可以快速跳转至某页 | boolean \| { goButton: ReactNode } | false |
| showTotal | 用于显示数据总量和当前数据顺序 | function(total, range) | - |
| onChange | 页码改变的回调 | function(page, pageSize) | - |
| onShowSizeChange | pageSize 变化的回调 | function(current, size) | - |

## Tabs 标签页

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| activeKey | 当前激活 tab 面板的 key | string | - |
| defaultActiveKey | 初始化选中面板的 key | string | 第一个面板 |
| type | 页签的基本样式 | `line` \| `card` \| `editable-card` | `line` |
| centered | 标签居中展示 | boolean | false |
| tabBarExtraContent | tab bar 上额外的元素 | {left?: ReactNode, right?: ReactNode} | - |
| onChange | 切换面板的回调 | function (activeKey) {} | - |
| onTabClick | tab 被点击的回调 | function (key: string, event: MouseEvent) | - |
| onEdit | 新增和删除页签的回调 | (targetKey, action): void | - |

### TabPane 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| key | 对应 activeKey | string | - |
| tab | 选项卡头显示文字 | string \| ReactNode | - |
| disabled | 选项卡头不可点 | boolean | false |
| forceRender | 被隐藏时是否渲染 DOM 结构 | boolean | false |
| closeIcon | 自定义关闭图标 | ReactNode | - |
| max | 选项卡头最多显示文字字数 | number | - |

## Select 选择器

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 指定当前选中的条目 | string \| string[] \| number \| number[] \| LabeledValue \| LabeledValue[] | - |
| defaultValue | 指定默认选中的条目 | string \| string[] \| number \| number[] \| LabeledValue \| LabeledValue[] | - |
| mode | 设置 Select 的模式 | `multiple` | - |
| options | 数据化配置选项内容 | {label, value}[] | - |
| placeholder | 选择框默认文本 | string | "请选择" |
| allowClear | 支持清除 | boolean | false |
| disabled | 是否禁用 | boolean | false |
| showSearch | 使单选模式可搜索 | boolean | false |
| showArrow | 是否显示下拉小箭头 | boolean | true |
| onChange | 选中 option 时调用此函数 | function (value, option) | - |
| onSearch | 文本框值变化时回调 | function (value: string) | - |
| onSelect | 被选中时调用 | function (value, option) | - |

### Option 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 默认根据此属性值进行筛选 | string \| number | - |
| disabled | 是否禁用 | boolean | false |
| title | 选中该 Option 后的 title | string | - |
| className | Option 类名 | string | - |
| extra | 额外的元素 | ReactNode | - |

## Search 搜索

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| multipleOption | 多条件搜索 | LabeledValue[] | [] |
| multipleValue | 多条件搜索值 | string \| number | - |
| multipleDefaultValue | 多条件搜索默认值 | string \| number | - |
| addonSelectStyle | 设置条件选择器的样式 | React.CSSProperties | - |
| onChangeMultiple | 多条件搜索change | (e: string \| number): void | - |
| onSearch | 点击搜索图标、清除图标，或按下回车键时的回调 | (e: any): void | - |

## Checkbox 复选框

### Checkbox 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| checked | 指定当前是否选中 | boolean | false |
| defaultChecked | 初始是否选中 | boolean | false |
| disabled | 失效状态 | boolean | false |
| indeterminate | 设置 indeterminate 状态 | boolean | false |
| onChange | 变化时回调函数 | function (e:Event) | - |

### CheckboxGroup 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 指定选中的选项 | string[] | [] |
| defaultValue | 默认选中的选项 | string[] | [] |
| options | 指定可选项 | string[] \| Option[] | [] |
| disabled | 整组失效 | boolean | false |
| onChange | 变化时回调函数 | function (checkedValue) | - |

## Input 输入框

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 输入框内容 | string | - |
| defaultValue | 输入框默认内容 | string | - |
| placeholder | 占位文本 | string | - |
| size | 控件大小 | `large` \| `middle` \| `small` | `middle` |
| disabled | 是否禁用 | boolean | false |
| readOnly | 是否只读 | boolean | false |
| allowClear | 可以点击清除图标删除内容 | boolean | false |
| prefix | 带有前缀图标的 input | ReactNode | - |
| suffix | 带有后缀图标的 input | ReactNode | - |
| addonBefore | 设置前置标签 | ReactNode | - |
| addonAfter | 设置后置标签 | ReactNode | - |
| onChange | 输入框内容变化时的回调 | function(e) | - |
| onPressEnter | 按下回车的回调 | function(e) | - |

### 不存在Input.Search，使用suffix或prefix属性

### Input.TextArea 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| autoSize | 自适应内容高度 | boolean \| { minRows: number, maxRows: number } | false |
| showCount | 是否展示字数 | boolean | false |
| value | 输入框内容 | string | - |
| defaultValue | 输入框默认内容 | string | - |
| allowClear | 可以点击清除图标删除内容 | boolean | true |
| onChange | 输入框内容变化时的回调 | function(e) | - |

### Input.Password 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| visibilityToggle | 是否显示切换按钮 | boolean | true |
| iconRender | 自定义切换按钮 | (visible) => ReactNode | - |

## InputNumber 数字输入框

### 属性

| 成员 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 当前值 | number | - |
| defaultValue | 初始值 | number | - |
| min | 最小值 | number | Number.MIN_SAFE_INTEGER |
| max | 最大值 | number | Number.MAX_SAFE_INTEGER |
| step | 每次改变步数，可以为小数 | number \| string | 1 |
| precision | 数值精度 | number | - |
| disabled | 禁用 | boolean | false |
| readOnly | 只读 | boolean | false |
| size | 输入框大小 | `large` \| `middle` \| `small` | - |
| onChange | 变化回调 | function(value: number \| string \| null) | - |
| onStep | 点击上下箭头的回调 | (value, info: { offset, type }) => void | - |

## Switch 开关

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| checked | 指定当前是否选中 | boolean | false |
| defaultChecked | 初始是否选中 | boolean | false |
| disabled | 是否禁用 | boolean | false |
| loading | 加载中的开关 | boolean | false |
| size | 开关大小 | `default` \| `small` | `default` |
| checkedChildren | 选中时的内容 | ReactNode | - |
| unCheckedChildren | 非选中时的内容 | ReactNode | - |
| onChange | 变化时回调函数 | function(checked, event) | - |
| onClick | 点击时回调函数 | function(checked, event) | - |


## Modal 对话框

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| visible | 对话框是否可见 | boolean | - |
| title | 标题 | ReactNode | - |
| width | 宽度 | string \| number | 520 |
| size | 尺寸 | 'small' \| 'normal' \| 'large' \| 'extraLarge' | 'small' |
| centered | 垂直居中展示 | boolean | false |
| closable | 是否显示右上角的关闭按钮 | boolean | true |
| confirmLoading | 确定按钮 loading | boolean | false |
| destroyOnClose | 关闭时销毁 Modal 里的子元素 | boolean | false |
| footer | 底部内容 | ReactNode | (确定取消按钮) |
| maskClosable | 点击蒙层是否允许关闭 | boolean | false |
| okText | 确认按钮文字 | ReactNode | `确定` |
| cancelText | 取消按钮文字 | ReactNode | `取消` |
| okButtonProps | 确认按钮 props | ButtonProps | - |
| cancelButtonProps | 取消按钮 props | ButtonProps | - |
| onOk | 点击确定回调 | function(e) | - |
| onCancel | 点击遮罩层或右上角叉或取消按钮的回调 | function(e) | - |

### 静态方法

- `Modal.confirm(config)`
- `Modal.success(config)`
- `Modal.info(config)`
- `Modal.error(config)`
- `Modal.warning(config)`

## Slider 滑动输入条

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| value | 设置当前取值 | number \| [number, number] | - |
| defaultValue | 设置初始取值 | number \| [number, number] | 0 \| [0, 0] |
| disabled | 值为 true 时，滑块为禁用状态 | boolean | false |
| range | 双滑块模式 | boolean \| range | false |
| step | 步长 | number \| null | 1 |
| min | 最小值 | number | 0 |
| max | 最大值 | number | 100 |
| marks | 刻度标记 | object | - |
| vertical | 值为 true 时，Slider 为垂直方向 | boolean | false |
| onChange | 当 Slider 的值发生改变时触发 | (value) => void | - |
| onAfterChange | 与 onmouseup 触发时机一致 | (value) => void | - |

## Form 表单控件

### Form 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| keepDisplayExtra | 是否一直展示 extra，此为 Form 配置，Form.Item 可单独配置 | boolean | false |
| colon | 配置 Form.Item 的 colon 的默认值。表示是否显示 label 后面的冒号 (只有在属性 layout 为 horizontal 时有效) | boolean | true |
| cols | 表单多列布局列数设置 | number | 1 |
| component | 设置 Form 渲染元素，为 false 则不创建 DOM 节点 | ComponentType \| false | form |
| fields | 通过状态管理（如 redux）控制表单字段，如非强需求不推荐使用 | FieldData[] | - |
| form | 经 Form.useForm() 创建的 form 控制实例，不提供时会自动创建 | FormInstance | - |
| initialValues | 表单默认值，只有初始化以及重置时生效 | object | - |
| labelAlign | label 标签的文本对齐方式 | `left` \| `right` | `right` |
| labelCol | label 标签布局，同 <Col> 组件，设置 span offset 值，如 {span: 3, offset: 12} 或 sm: {span: 3, offset: 12} | object | - |
| labelWidth | label 标签宽度 | number \| string | - |
| inputMaxWidth | 输入控件默认最大宽度 | number \| string | 400px |
| layout | 表单布局 | `horizontal` \| `vertical` \| `inline` | `horizontal` |
| name | 表单名称，会作为表单字段 id 前缀使用 | string | - |
| preserve | 当字段被删除时保留字段值 | boolean | true |
| requiredMark | 必选样式，可以切换为必选或者可选展示样式。此为 Form 配置，Form.Item 无法单独配置 | boolean \| optional | true |
| scrollToFirstError | 提交失败自动滚动到第一个错误字段 | boolean \| Options | false |
| size | 设置字段组件的尺寸（仅限 acud 组件） | `small` \| `middle` \| `large` \| `tiny` | - |
| validateMessages | 验证提示模板 | ValidateMessages | - |
| validateTrigger | 统一设置字段触发验证的时机 | string \| string[] | onChange |
| wrapperCol | 需要为输入控件设置布局样式时，使用该属性，用法同 labelCol | object | - |
| onFieldsChange | 字段更新时触发回调事件 | function(changedFields, allFields) | - |
| onFinish | 提交表单且数据验证成功后回调事件 | function(values) | - |
| onFinishFailed | 提交表单且数据验证失败后回调事件 | function({ values, errorFields, outOfDate }) | - |
| onValuesChange | 字段值更新时触发回调事件 | function(changedValues, allValues) | - |

### Form.Item 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| keepDisplayExtra | 是否一直展示 extra | boolean | false |
| colon | 配合 label 属性使用，表示是否显示 label 后面的冒号 | boolean | true |
| dependencies | 设置依赖字段 | NamePath[] | - |
| extra | 额外的提示信息，和 help 类似，默认校验不通过时，展示错误信息，不展示该提示文案，若需要该提示文案一直存在，可设置keepDisplayExtra为true | ReactNode | - |
| getValueFromEvent | 设置如何将 event 的值转换成字段值 | (..args: any[]) => any | - |
| getValueProps | 为子元素添加额外的属性 | (value: any) => any | - |
| hasFeedback | 配合 validateStatus 属性使用，展示校验状态图标，建议只配合 Input 组件使用 | boolean | false |
| help | 提示信息，如不设置，则会根据校验规则自动生成 | ReactNode | - |
| hidden | 是否隐藏字段（依然会收集和校验字段） | boolean | false |
| htmlFor | 设置子元素 label htmlFor 属性 | string | - |
| initialValue | 设置子元素默认值，如果与 Form 的 initialValues 冲突则以 Form 为准 | string | - |
| label | label 标签的文本 | ReactNode | - |
| labelAlign | 标签文本对齐方式 | `left` \| `right` | `right` |
| labelCol | label 标签布局，同 <Col> 组件，设置 span offset 值，如 {span: 3, offset: 12} 或 sm: {span: 3, offset: 12}。你可以通过 Form 的 labelCol 进行统一设置，不会作用于嵌套 Item。当和 Form 同时设置时，以 Item 为准 | object | - |
| labelWidth | label 标签宽度 | number \| string | - |
| labelExtra | 只在form是vertical布局时，label区域右侧的自定义组件 | React.ReactElement | - |
| inputMaxWidth | 输入控件默认最大宽度 | number \| string | 400px |
| messageVariables | 默认验证字段的信息 | Record<string, string> | - |
| name | 字段名，支持数组 | NamePath | - |
| normalize | 组件获取值后进行转换，再放入 Form 中。不支持异步 | (value, prevValue, prevValues) => any | - |
| noStyle | 为 true 时不带样式，作为纯字段控件使用 | boolean | false |
| preserve | 当字段被删除时保留字段值 | boolean | true |
| required | 必填样式设置。如不设置，则会根据校验规则自动生成 | boolean | false |
| rules | 校验规则，设置字段的校验逻辑 | Rule[] | - |
| shouldUpdate | 自定义字段更新逻辑 | boolean \| (prevValue, curValue) => boolean | false |
| tooltip | 配置提示信息 | ReactNode \| TooltipProps & { icon: ReactNode } | - |
| trigger | 设置收集字段值变更的时机 | string | onChange |
| validateFirst | 当某一规则校验不通过时，是否停止剩下的规则的校验。设置 parallel 时会并行校验 | boolean \| parallel | false |
| validateStatus | 校验状态，如不设置，则会根据校验规则自动生成，可选：'success' 'warning' 'error' 'validating' | string | - |
| validateTrigger | 设置字段校验的时机 | string \| string[] | onChange |
| valuePropName | 子节点的值的属性，如 Switch 的是 'checked'。该属性为 getValueProps 的封装，自定义 getValueProps 后会失效 | string | value |
| wrapperCol | 需要为输入控件设置布局样式时，使用该属性，用法同 labelCol。你可以通过 Form 的 wrapperCol 进行统一设置，不会作用于嵌套 Item。当和 Form 同时设置时，以 Item 为准 | object | - |

### 示例

```jsx
<Form
    {...formItemLayout}
    layout={formLayout === 'vertical' ? formLayout : 'horizontal'}
    name="basic"
    initialValues={{
        layout: 'left',
        passwordType: 'USER'
    }}
    onFinish={onFinish}
    onFinishFailed={onFinishFailed}
>
    <Form.Item
        label="表单布局"
        name="layout"
    >
        <Radio.Group
            options={layoutOptions}
            optionType="button"
            onChange={onFormLayoutChange}
        />
    </Form.Item>

    <Form.Item
        label="管理员密码"
        name="passwordType"
        rules={[{required: true, message: '请输入管理员密码'}]}
    >
        <Radio.Group
            options={passwordOptions}
            optionType="button"
        />
    </Form.Item>

    <Form.Item
        label="自定义密码"
        name="password"
        rules={[
            {required: true, message: '请输入密码'},
            {pattern: /\d{3,10}/, message: '请输入正确的密码'}
        ]}
        extra="3-10位数字"
    >
        <Input.Password style={{width: '340px'}} />
    </Form.Item>

    <Form.Item
        label="备注"
        name="remark"
        rules={[
            {required: true, message: '请输入备注信息'}
        ]}
    >
        <Input.TextArea style={{width: '340px'}} />
    </Form.Item>

    <Form.Item
        label="transfer"
        name="transfer"
        rules={[
            {required: true, message: '请选择数据'}
        ]}
    >
        <Transfer onChange={transferChange} dataSource={[{key: 1, title: 'data1'}, {key: 2, title: 'data2'}]} />
    </Form.Item>

    <Form.Item
        label="treeselect"
        name="treeselect"
    >
        <TreeSelect style={{width: 300}}>
            <TreeNode value="parent 1" title="parent 1">
                <TreeNode value="parent 1-0" title="parent 1-0">
                    <TreeNode value="leaf1" title="leaf1" />
                    <TreeNode value="leaf2" title="leaf2" />
                </TreeNode>
                <TreeNode value="parent 1-1" title="parent 1-1">
                    <TreeNode value="leaf3" title={<b style={{color: '#08c'}}>leaf3</b>} />
                </TreeNode>
            </TreeNode>
        </TreeSelect>
    </Form.Item>

    <Form.Item label="">
        <Button type="primary" htmlType="submit">
            Submit
        </Button>
    </Form.Item>
</Form>
```

## Table 表格

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| dataSource | 数据数组 | object[] | - |
| columns | 表格列的配置描述 | ColumnsType[] | - |
| rowKey | 表格行 key 的取值 | string \| function(record): string | `key` |
| pagination | 分页器 | object \| false | - |
| loading | 页面是否加载中 | boolean \| LoadingProps | false |
| size | 表格大小 | `default` \| `large` \| `small` | default |
| bordered | 是否展示外边框和列边框 | boolean | false |
| rowSelection | 表格行是否可选择 | object | - |
| scroll | 表格是否可滚动 | { x?: number \| true, y?: number } | - |
| onChange | 分页、排序、筛选变化时触发 | function(pagination, filters, sorter, extra) | - |

### Column 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| title | 列头显示文字 | ReactNode \| ({ sortOrder, sortColumn, filters }) => ReactNode | - |
| dataIndex | 列数据在数据项中对应的路径 | string \| string[] | - |
| key | React 需要的 key | string | - |
| align | 设置列的对齐方式 | `left` \| `right` \| `center` | `left` |
| width | 列宽度 | string \| number | - |
| fixed | 列是否固定 | boolean \| `left` \| `right` | false |
| render | 生成复杂数据的渲染函数 | function(text, record, index) {} | - |
| sorter | 排序函数 | function \| boolean | - |
| filters | 表头的筛选菜单项 | object[] | - |
| onFilter | 本地模式下，确定筛选的运行函数 | function | - |

## Toast 消息提示

### 方法

- `toast.success(config)` - 成功提示
- `toast.error(config)` - 错误提示
- `toast.info(config)` - 信息提示
- `toast.warning(config)` - 警告提示
- `toast.open(config)` - 打开提示
- `toast.destroy(key?: String)` - 销毁提示

### 配置参数

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| message | 通知提醒标题 | ReactNode | - |
| description | 通知提醒内容 | ReactNode | - |
| duration | 自动关闭的延时，不设置则不自动关闭 | number | - |
| icon | 自定义图标 | ReactNode | - |
| key | 当前通知唯一标志 | string | - |
| className | 自定义 CSS class | string | - |
| onClick | 点击通知时触发的回调函数 | function | - |
| onClose | 当通知关闭时触发 | function | - |

### 全局配置

```js
toast.config({
  prefixCls: '',
  placement: 'bottomRight',
  bottom: 50,
  maxCount: 4,
});
```
|getContainer|配置渲染节点的输出位置|() => HTMLNode|() => document.body||
|placement|弹出位置，可选 topLeft topRight bottomLeft bottomRight|string|topRight||
|top|消息从顶部弹出时，距离顶部的位置，单位像素|number|24||
|maxCount|最大显示数|number|4||
|autoClose|设置为false时,该toast将不会自动关闭|boolean|true||

## Tag 标签

用于标记和选择。

### 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| prefixCls | 设置 class 前缀 | string | `acud-tag` |
| className | 设置 class 名称 | string | - |
| color | 标签色 | string | - |
| size | 标签大小 | `large` \| `middle` \| `small` | `middle` |
| closable | 标签是否可以关闭（点击默认关闭） | boolean | false |
| closeIcon | 自定义关闭按钮 | ReactNode | - |
| visible | 是否显示标签 | boolean | true |
| disabled | 是否禁用 | boolean | false |
| onClose | 关闭时的回调（可通过 e.preventDefault() 来阻止默认行为） | (e) => void | - |
| style | 设置标签的样式 | React.CSSProperties | - |
| icon | 设置图标 | ReactNode | - |
| type | 设置标签定制类型 | string | - |

### Tag.CheckableTag 属性

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| prefixCls | 设置 class 前缀 | string | `acud-tag` |
| transparent | 标签是否透明 | boolean | false |
| className | 设置 class 名称 | string | - |
| checked | 设置标签的选中状态 | boolean | false |
| onChange | 点击标签时触发的回调 | (checked) => void | - |
| onClick | 点击标签时的回调 | (event) => void | - |

### 示例

```jsx
import {Tag} from 'acud';
import {OutlinedButtonRefresh, OutlinedCheckCircle, OutlinedCloseCircle, OutlinedInfoCircle} from 'acud-icon';

const {TagList} = Tag;
const tagListData = [
    {text: '标签'},
    {text: '标签'},
    {text: '标签'},
    {text: '标签'},
    {text: '标签'},
    {text: '标签'},
    {text: '标签'},
    {text: '标签'},
    {text: '标签'},
    {text: '标签'}
];

// 普通标签
<Tag color="active">激活</Tag>
<Tag color="error">负向</Tag>
<Tag color="success">正向</Tag>
<Tag color="warning">异常</Tag>

// 描边标签
<Tag color="active-outline">激活</Tag>
<Tag color="error-outline">负向</Tag>
<Tag color="success-outline">正向</Tag>
<Tag color="warning-outline">异常</Tag>

// 增强标签
<Tag color="active-enhance">激活</Tag>
<Tag color="error-enhance">负向</Tag>
<Tag color="success-enhance">正向</Tag>
<Tag color="warning-enhance">异常</Tag>

// 状态标签
<Tag color="processing-status" icon={<OutlinedButtonRefresh animation="spin" />}>执行中</Tag>
<Tag color="success-status" icon={<OutlinedCheckCircle />}>已完成</Tag>
<Tag color="error-status" icon={<OutlinedCloseCircle />}>执行失败</Tag>
<Tag color="warning-status" icon={<OutlinedInfoCircle />}>执行异常</Tag>

// 透明标签
<Tag color="transparent" icon={<span className="circle status-active"></span>}>待执行</Tag>
<Tag color="transparent" icon={<span className="circle status-processing"></span>}>执行中</Tag>

// 标签列表
<TagList tagDatas={tagListData} />
<TagList tagDatas={tagListData} type="outline" />
<TagList tagDatas={tagListData} type="enhance" />
```
