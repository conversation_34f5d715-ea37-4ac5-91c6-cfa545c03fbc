---
description: 
globs: 
alwaysApply: true
type: "always_apply"
---
# 本文档用于规范接口文档的生成规范

1. 我会处理非列表接口（list） 接口的格式转换，你只需要把响应体放在 response 里面即可： 
        {{with body}}
            {{put "success" true}}
            {{put "result" $.response}}
            {{put "status" 200}}
            {{printBody}}
        {{end}}

2. 如果涉及到查询列表接口，所有返回参数需要包含在 page 结构体中，需要返回 页码PageNo、排序方式order（倒序、顺序）、排序字段orderBy（基于什么字段排序）、单页条数(pageSize)、总数(totalCount),参考以下示例：
            {
                "success": true,
                "status": 200,
                "page": {
                    "orderBy": "createTime",
                    "order": "desc",
                    "pageNo": 1,
                    "pageSize": 10,
                    "totalCount": 0,
                    "result": []
                }
            }

】

3. 关于这个项目，所有接口的路径前缀指定为 /api/aigw/v1/aigateway

4. 如果请求失败，会返回如下格式的响应,在前端页面要正确处理这个结构的错误响应：
{
    "success": false,
    "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
    "code": "InvalidParameterValueException",
    "message": {
        "global": "One of the parameters in the request is invalid"
    }
}

5. request从'@baidu/bce-react-toolkit'中引入