---
type: "always_apply"
---

# ACUD组件库速查

ACUD是百度内部的React UI组件库，提供了丰富的UI组件用于构建企业级应用。引入组件时请使用：`import { ComponentName } from 'acud'`

## 组件速查表

### 通用组件
- **Button** - 按钮：primary/default/text/actiontext/highlight/enhance
- **Link** - 超链接：primary/default/text
- **Divider** - 分割线：水平/垂直，实线/虚线

### 布局组件
- **Menu** - 导航菜单：垂直/水平/内联模式
- **Tabs** - 标签页：line/card/editable-card

### 数据录入组件
- **Input** - 输入框
    - Input.TextArea
    - Input.Password
    - Input.Label
    - 不支持Input.Search
- **InputNumber** - 数字输入框
- **Select** - 下拉选择器：单选/多选
- **Checkbox** - 多选框：单个/组
- **Radio** - 单选框：单个/组
- **Switch** - 开关
- **Slider** - 滑动输入条：单滑块/双滑块
- **Form** - 表单 horizontal/vertical

### 数据展示组件
- **Table** - 表格：可排序/筛选/分页
- **Pagination** - 分页
- **Steps** - 步骤条：水平/垂直
- **Empty** - 空状态
- **Tag** - 表示目标对象某种属性的提示性控件，如状态、类别等。

### 反馈组件
- **Modal** - 对话框：信息/确认/成功/错误/警告
- **toast** - 轻提示：success/error/info/warning
- **Tooltip** - 文字提示
- **Popover** - 气泡卡片
- **Popconfirm** - 气泡确认框

