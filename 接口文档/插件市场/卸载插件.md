# 卸载插件

## 接口描述
根据插件名称卸载插件，全量删除该插件的所有规则。目前支持外部认证插件的卸载。

## 请求信息

### 请求路径
```
DELETE /api/aigw/v1/aigateway/{instanceId}/pluginMarket/{pluginName}
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |
| pluginName | string | 是 | 插件名称，目前仅支持：ext-auth |

### 请求示例
```
DELETE /api/aigw/v1/aigateway/instance123/pluginMarket/ext-auth
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| status | integer | HTTP状态码 |
| message | string | 响应消息 |
| pluginName | string | 插件名称 |
| deletedCount | integer | 删除的规则数量 |
| uninstallTime | string | 卸载时间 |

### 响应示例

#### 成功响应
```json
{
  "result": {
    "deletedCount": 1,
    "message": "插件卸载成功，共删除 1 个规则",
    "pluginName": "ext-auth",
    "uninstallTime": "2025-08-01 11:51:32"
  },
  "status": 200,
  "success": true
}
```

#### 部分成功响应
```json
{
  "result": {
    "message": "插件卸载部分成功，成功删除 3 个规则，失败 2 个",
    "pluginName": "ext-auth",
    "deletedCount": 3,
    "uninstallTime": "2024-01-15 14:30:25"
  },
  "status": 200,
  "success": true
}
```

#### 无插件时响应
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "ResourceNotFoundException",
  "message": {
    "global": "未找到需要卸载的插件"
  }
}
```

## 错误码

### 通用错误码
- `MissingParametersException`: 缺少必要参数
- `InvalidParameterValueException`: 参数值无效
- `ResourceNotFoundException`: 未找到需要卸载的插件

### 错误响应示例
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "InvalidParameterValueException",
  "message": {
    "global": "不支持的插件名称，目前仅支持: ext-auth"
  }
}
```