# 查询外部认证插件列表

## 接口描述
查询指定AI网关实例下的外部认证插件列表。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/extAuthList
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |

#### 查询参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| name | string | 否 | 插件名称过滤器，支持模糊匹配 |
| scope | string | 否 | 生效粒度过滤，gateway/route，不传则返回所有 |
| pageNo | integer | 否 | 页码，默认1 |
| pageSize | integer | 否 | 每页条数，默认10，最大100 |
| orderBy | string | 否 | 排序字段，默认createTime |
| order | string | 否 | 排序方式，可选值：asc、desc，默认desc |

### 请求示例
```
GET /api/aigw/v1/aigateway/instance123/extAuthList?name=auth&pageNo=1&pageSize=10
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| status | integer | HTTP状态码 |
| page | object | 分页信息 |
| page.orderBy | string | 排序字段 |
| page.order | string | 排序方式 |
| page.pageNo | integer | 当前页码 |
| page.pageSize | integer | 每页条数 |
| page.totalCount | integer | 总记录数 |
| page.result | array | 插件列表数据 |

#### 插件列表项参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| name | string | 插件名称 |
| enabled | boolean | 是否启用 |
| scope | string | 生效粒度 |
| matchType | string | 匹配类型（仅路由维度） |
| httpService | string | HTTP服务配置（YAML格式字符串） |
| statusOnError | integer | 认证失败状态码 |
| createTime | string | 创建时间 |
| updateTime | string | 更新时间 |

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "status": 200,
  "page": {
    "orderBy": "createTime",
    "order": "desc",
    "pageNo": 1,
    "pageSize": 10,
    "totalCount": 2,
    "result": [
      {
        "name": "my-gateway-auth",
        "enabled": true,
        "scope": "gateway",
        "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /auth\ntimeout: 2000",
        "statusOnError": 403,
        "createTime": "2024-01-15 10:30:00",
        "updateTime": "2024-01-15 10:30:00"
      },
      {
        "name": "my-route-auth",
        "enabled": false,
        "scope": "route",
        "matchType": "blacklist",
        "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /validate\ntimeout: 2000",
        "statusOnError": 401,
        "createTime": "2024-01-15 09:15:00",
        "updateTime": "2024-01-15 11:20:00"
      }
    ]
  }
}
```

#### 空列表响应
```json
{
  "success": true,
  "status": 200,
  "page": {
    "orderBy": "createTime",
    "order": "desc",
    "pageNo": 1,
    "pageSize": 10,
    "totalCount": 0,
    "result": []
  }
}
```

#### 失败响应
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "InvalidParameterValueException",
  "message": {
    "global": "One of the parameters in the request is invalid"
  }
}
```

## 使用说明

1. **分页查询**: 支持标准的分页查询，通过pageNo和pageSize控制
2. **排序**: 支持按创建时间排序，可指定升序或降序
3. **过滤**: 支持按插件名称进行模糊匹配过滤
4. **维度过滤**: 支持按生效粒度过滤，可选值：
   - `gateway`: 仅返回网关维度的插件
   - `route`: 仅返回路由维度的插件
   - 不传: 返回所有维度的插件
5. **权限**: 需要通过IAM签名验证

### 请求示例

#### 查询所有外部认证插件
```
GET /api/aigw/v1/aigateway/instance123/extAuthList
```

#### 查询网关维度的外部认证插件
```
GET /api/aigw/v1/aigateway/instance123/extAuthList?scope=gateway
```

#### 查询路由维度的外部认证插件
```
GET /api/aigw/v1/aigateway/instance123/extAuthList?scope=route&pageNo=1&pageSize=5
```

## 错误码
| 错误码 | 描述 |
|--------|------|
| MissingParametersException | 缺少必要参数 |
| InvalidParameterValueException | 参数值无效 |
| InternalServerError | 服务器内部错误 |
