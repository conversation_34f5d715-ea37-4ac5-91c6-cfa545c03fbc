# 查询路由列表（含外部认证状态）

## 接口描述
查询AI网关实例下的路由列表，包含每个路由是否关联外部认证插件的状态信息。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/routes
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |


### 请求示例
```
GET /api/aigw/v1/aigateway/instance123/routes
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| status | integer | HTTP状态码 |
| result | array | 路由列表 |
| result[].routeName | string | 路由名称 |
| result[].hasExtAuth | boolean | 是否关联外部认证插件 |

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "status": 200,
  "result": [
    {
      "routeName": "api-route-1",
      "hasExtAuth": true
    },
    {
      "routeName": "api-route-2",
      "hasExtAuth": false
    },
    {
      "routeName": "web-route-1",
      "hasExtAuth": true
    },
    {
      "routeName": "web-route-2",
      "hasExtAuth": false
    },
    {
      "routeName": "admin-route",
      "hasExtAuth": true
    }
  ]
}
```

#### 无路由时响应
```json
{
  "success": true,
  "status": 200,
  "result": []
}
```

## 错误码

### 通用错误码
- `MissingParametersException`: 缺少必要参数
- `InvalidParameterValueException`: 参数值无效

### 错误响应示例
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "MissingParametersException",
  "message": {
    "global": "instanceId is required"
  }
}
```