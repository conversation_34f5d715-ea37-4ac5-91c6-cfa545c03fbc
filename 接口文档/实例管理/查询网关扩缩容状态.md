# 查询网关扩缩容状态

## 接口描述
查询指定AI网关实例的Higress Gateway Deployment的扩缩容状态信息，用于监控网关实例的健康状况和部署进度。

## 请求信息

### 请求路径
```
GET /api/aigw/v1/aigateway/{instanceId}/gateway/status
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |

### 请求示例
```
GET /api/aigw/v1/aigateway/instance123/gateway/status
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| ready | integer | 当前就绪的副本数（对应Kubernetes Deployment的READY状态） |
| upToDate | integer | 已更新到最新版本的副本数（对应UP-TO-DATE状态） |
| available | integer | 当前可用的副本数（对应AVAILABLE状态） |
| replicas | integer | 期望的副本数（总副本数） |

### 状态字段说明
- **ready**: 当前处于就绪状态的Pod副本数，表示已经通过健康检查并可以接收流量的副本
- **upToDate**: 已经更新到最新版本的Pod副本数，在滚动更新过程中此值会逐步增加
- **available**: 当前可用的Pod副本数，表示可以正常提供服务的副本
- **replicas**: 期望的总副本数，由Deployment配置中的replicas字段决定

### 响应示例

#### 正常运行状态
```json
{
  "result": {
      "available": 2,
      "ready": 2,
      "replicas": 2,
      "upToDate": 2
  },
  "status": 200,
  "success": true
}
```

#### 扩容进行中
```json
{
  "result": {
      "available": 2,
      "ready": 2,
      "replicas": 5,
      "upToDate": 5
  },
  "status": 200,
  "success": true
}
```


#### 部分副本异常
```json
{
  "result": {
      "available": 2,
      "ready": 3,
      "replicas": 3,
      "upToDate": 3
  },
  "status": 200,
  "success": true
}
```

## 错误码

### 通用错误码
- `MissingParametersException`: 缺少必要参数
- `ResourceNotFoundException`: 网关Deployment不存在
- `DBOperationException`: Kubernetes API操作失败

### 错误响应示例

#### 实例ID缺失
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "MissingParametersException",
  "message": {
    "global": "instanceId is required"
  }
}
```

#### 网关Deployment不存在
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "ResourceNotFoundException",
  "message": {
    "global": "higress-gateway deployment not found"
  }
}
```
