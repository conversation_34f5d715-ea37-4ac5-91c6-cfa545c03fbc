# 创建网关实例接口

## 接口基本信息

- **接口说明**：创建一个新的AI网关实例
- **接口地址**：`/api/aigw/v1/aigateway`
- **请求方式**：POST
- **数据格式**：JSON

## 请求参数

### 请求头

| 参数名 | 类型 | 是否必填 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| Content-Type | String | 是 | 内容类型 | application/json |
| X-Region | String | 是 | 网关实例所属地域 | gz |

### 请求体参数

```json
{
    "name": "ai-gateway-0413",
    "vpcId": "vpc-55dfhgtw0x",
    "vpcCidr": "***********/24",
    "subnetId": "sbn-suwftkzv543e",
    "gatewayType": "small",
    "isInternal": "true",
    "replicas": 1,
    "description": "生产环境订单系统网关",
    "deleteProtection": true,
    "clusters": []
}
```

| 参数名 | 类型 | 是否必填 | 描述 | 限制条件 |
| --- | --- | --- | --- | --- |
| name | String | 是 | 实例名称 | 1. 必须以字母或中文开头<br>2. 支持大小写字母、数字、中文以及-_/.特殊字符<br>3. 长度范围：1-64个字符<br>4. 不能与已有网关名称重复 |
| vpcId | String | 是 | 私有网络ID | 必须是有效的VPC ID |
| vpcCidr | String | 是 | VPC网段 | 符合CIDR格式的IP网段，如***********/24 |
| subnetId | String | 是 | 子网ID | 必须是指定VPC下有效的子网ID |
| gatewayType | String | 是 | 网关规格 | 当前仅支持"small"一种规格 |
| isInternal | String | 否 | 是否仅内网访问 | "true"或"false"，默认为"true" |
| replicas | Integer | 否 | 节点数量 | 合法范围：[1,5]，默认为2 |
| description | String | 否 | 网关实例描述 | 最多64个字符 |
| deleteProtection | Boolean | 否 | 网关删除保护 | true(开启)或false(关闭)，默认为true |
| clusters | Array | 否 | 关联的集群列表 | 小型规格最多关联1个集群 |

#### clusters参数说明

| 参数名 | 类型 | 是否必填 | 描述 | 限制条件 |
| --- | --- | --- | --- | --- |
| clusterId | String | 是 | 集群ID | 必须是有效的CCE集群ID |
| clusterName | String | 是 | 集群名称 | - |
| remark | String | 否 | 关联备注 | 最多64个字符 |

## 响应参数

### 响应体结构

```json
{
    "success": true,
    "status": 200,
    "result": {
        "instanceId": "i-a1b2c3d4",
        "requestId": "req-a1b2c3d4e5f6",
        "taskId": "task-a1b2c3d4e5"
    }
}
```

### 结果字段说明

| 字段名 | 类型 | 描述 |
| --- | --- | --- |
| success | Boolean | 请求是否成功 |
| status | Integer | 状态码 |
| result | Object | 结果对象 |
| result.instanceId | String | 创建的网关实例ID |
| result.requestId | String | 请求ID，用于跟踪请求 |
| result.taskId | String | 创建任务ID，可用于查询创建进度 |

## 错误码

| 错误码 | 描述 | 解决方案 |
| --- | --- | --- |
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 确认用户是否有创建网关实例的权限 |
| 403 | 配额不足 | 检查是否达到了创建实例的配额限制 |
| 404 | 网络资源不存在 | 确认VPC或子网ID是否正确 |
| 409 | 名称冲突 | 更换一个未被使用的实例名称 |
| 500 | 服务器内部错误 | 请联系管理员 |

## 请求示例

```
POST /api/aigw/v1/aigateway
Content-Type: application/json
X-Region: gz

{
    "name": "ai-gateway-0413",
    "vpcId": "vpc-55dfhgtw0x",
    "vpcCidr": "***********/24",
    "subnetId": "sbn-suwftkzv543e",
    "gatewayType": "small",
    "isInternal": "true",
    "replicas": 2,
    "description": "生产环境订单系统网关",
    "deleteProtection": true,
    "clusters": []
}
```

## 响应示例

```json
{
    "success": true,
    "status": 200,
    "result": {
        "instanceId": "i-abcdefgh",
        "requestId": "req-abcdefgh12345",
        "taskId": "task-abcdefgh123"
    }
}
```

## 注意事项

1. 创建网关实例是一个异步操作，接口返回成功仅表示创建请求已被接受，可通过返回的taskId查询创建进度
2. 网关实例创建完成后，可能需要1-3分钟才能完全就绪并提供服务
3. 如需关联CCE集群，建议集群与网关实例在同一VPC下，以获得最佳网络性能
4. 网关规格决定了性能上限，请根据业务需求选择合适的规格
5. 建议节点数量不少于2个，以保证高可用性
6. 删除保护默认开启，开启后无法直接删除网关实例，需要先关闭删除保护
7. isInternal设为"false"时会自动创建EIP，可能产生额外费用 